{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunase<PERSON>an/frontend/src/app/admin/homepage/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Save, Eye, Type, Upload, Image as ImageIcon, Plus, Trash2, Target, MessageSquare } from 'lucide-react';\nimport Image from 'next/image';\n\ninterface ContentItem {\n  id?: string;\n  section: string;\n  key: string;\n  value: string;\n  type: 'text' | 'image';\n  metadata?: Record<string, unknown>;\n  order?: number;\n}\n\ninterface ExpertiseItem {\n  id?: string;\n  title: string;\n  description: string;\n  icon: string;\n  order: number;\n}\n\ninterface TestimonialItem {\n  id?: string;\n  text: string;\n  author: string;\n  company: string;\n  avatar?: string;\n  order: number;\n}\n\ninterface HomepageContent {\n  [key: string]: ContentItem[];\n}\n\nexport default function HomepageAdmin() {\n  const [content, setContent] = useState<HomepageContent>({\n    header: [],\n    hero: [],\n    what_we_do: [],\n    why_choose_us: [],\n    how_it_works: [],\n    our_expertise: [],\n    testimonials: [],\n    final_cta: [],\n    footer: []\n  });\n  const [originalContent, setOriginalContent] = useState<HomepageContent>({});\n  const [expertiseItems, setExpertiseItems] = useState<ExpertiseItem[]>([]);\n  const [testimonialItems, setTestimonialItems] = useState<TestimonialItem[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [activeSection, setActiveSection] = useState('hero');\n  const [uploadingImages, setUploadingImages] = useState<Record<string, boolean>>({});\n\n  useEffect(() => {\n    fetchHomepageContent();\n  }, []);\n\n  const fetchHomepageContent = async () => {\n    try {\n      const token = localStorage.getItem('adminToken');\n\n      // Fetch regular content\n      const contentResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/content`, {\n        headers: { 'Authorization': `Bearer ${token}` },\n      });\n\n      let apiContent: Record<string, Record<string, string>> = {};\n      if (contentResponse.ok) {\n        apiContent = await contentResponse.json();\n      }\n\n      // Fetch expertise items\n      const expertiseResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/expertise`, {\n        headers: { 'Authorization': `Bearer ${token}` },\n      });\n\n      let expertiseData: ExpertiseItem[] = [];\n      if (expertiseResponse.ok) {\n        const expertiseResult = await expertiseResponse.json();\n        expertiseData = expertiseResult.items || [];\n      }\n\n      // Fetch testimonials\n      const testimonialsResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/testimonials`, {\n        headers: { 'Authorization': `Bearer ${token}` },\n      });\n\n      let testimonialsData: TestimonialItem[] = [];\n      if (testimonialsResponse.ok) {\n        const testimonialsResult = await testimonialsResponse.json();\n        testimonialsData = testimonialsResult.items || [];\n      }\n\n      // Default content structure (text only, no images)\n      const defaultContent: HomepageContent = {\n        header: [\n          { section: 'header', key: 'company_name', value: 'RevAdOps', type: 'text' }\n        ],\n        hero: [\n          { section: 'hero', key: 'title', value: 'Unlock Your Ad Revenue Potential with Intelligent Ad Operations', type: 'text' },\n          { section: 'hero', key: 'subtitle', value: 'RevAdOps helps publishers and app developers maximize revenue, improve fill rates, and maintain healthy traffic quality through advanced AdTech solutions and data-driven optimization.', type: 'text' },\n          { section: 'hero', key: 'background_image', value: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2025&q=80', type: 'image' },\n          { section: 'hero', key: 'cta_primary_text', value: 'Get a Free Consultation', type: 'text' },\n          { section: 'hero', key: 'cta_primary_link', value: '/consultation', type: 'text' },\n          { section: 'hero', key: 'cta_secondary_text', value: 'Explore Our Solutions', type: 'text' },\n          { section: 'hero', key: 'cta_secondary_link', value: '/solutions', type: 'text' }\n        ],\n        what_we_do: [\n          { section: 'what_we_do', key: 'title', value: 'Your Partner in Smarter Ad Monetization', type: 'text' },\n          { section: 'what_we_do', key: 'description', value: 'At RevAdOps, we specialize in optimizing web, app, and video monetization strategies for publishers worldwide.', type: 'text' },\n          { section: 'what_we_do', key: 'service_1_title', value: 'Revenue Optimization', type: 'text' },\n          { section: 'what_we_do', key: 'service_1_description', value: 'Advanced algorithms and real-time bidding strategies to maximize your ad revenue potential.', type: 'text' },\n          { section: 'what_we_do', key: 'service_2_title', value: 'Ad Quality Control', type: 'text' },\n          { section: 'what_we_do', key: 'service_2_description', value: 'Comprehensive filtering and monitoring to ensure only high-quality ads reach your audience.', type: 'text' },\n          { section: 'what_we_do', key: 'service_3_title', value: 'Performance Analytics', type: 'text' },\n          { section: 'what_we_do', key: 'service_3_description', value: 'Detailed insights and reporting to track performance and identify optimization opportunities.', type: 'text' },\n          { section: 'what_we_do', key: 'service_4_title', value: 'Traffic Protection', type: 'text' },\n          { section: 'what_we_do', key: 'service_4_description', value: 'Advanced fraud detection and prevention to protect your traffic quality and advertiser relationships.', type: 'text' }\n        ],\n        why_choose_us: [\n          { section: 'why_choose_us', key: 'title', value: 'Why Choose RevAdOps?', type: 'text' },\n          { section: 'why_choose_us', key: 'description', value: 'We combine cutting-edge technology with industry expertise to deliver exceptional results for our clients.', type: 'text' },\n          { section: 'why_choose_us', key: 'reason_1_title', value: 'Proven Results', type: 'text' },\n          { section: 'why_choose_us', key: 'reason_1_description', value: 'Average 40% revenue increase within 90 days of implementation.', type: 'text' },\n          { section: 'why_choose_us', key: 'reason_2_title', value: 'Expert Team', type: 'text' },\n          { section: 'why_choose_us', key: 'reason_2_description', value: 'Dedicated AdTech specialists with 10+ years of industry experience.', type: 'text' },\n          { section: 'why_choose_us', key: 'reason_3_title', value: 'Award-Winning', type: 'text' },\n          { section: 'why_choose_us', key: 'reason_3_description', value: 'Recognized as a leading ad optimization platform by industry experts.', type: 'text' },\n          { section: 'why_choose_us', key: 'reason_4_title', value: '24/7 Support', type: 'text' },\n          { section: 'why_choose_us', key: 'reason_4_description', value: 'Round-the-clock monitoring and support to ensure optimal performance.', type: 'text' }\n        ],\n        how_it_works: [\n          { section: 'how_it_works', key: 'title', value: 'How It Works', type: 'text' },\n          { section: 'how_it_works', key: 'description', value: 'Our streamlined process ensures quick implementation and immediate results.', type: 'text' },\n          { section: 'how_it_works', key: 'step_1_title', value: 'Analysis', type: 'text' },\n          { section: 'how_it_works', key: 'step_1_description', value: 'We analyze your current ad setup and identify optimization opportunities.', type: 'text' },\n          { section: 'how_it_works', key: 'step_2_title', value: 'Strategy', type: 'text' },\n          { section: 'how_it_works', key: 'step_2_description', value: 'Develop a customized optimization strategy based on your specific needs.', type: 'text' },\n          { section: 'how_it_works', key: 'step_3_title', value: 'Implementation', type: 'text' },\n          { section: 'how_it_works', key: 'step_3_description', value: 'Deploy our solutions with minimal disruption to your current operations.', type: 'text' },\n          { section: 'how_it_works', key: 'step_4_title', value: 'Optimization', type: 'text' },\n          { section: 'how_it_works', key: 'step_4_description', value: 'Continuous monitoring and optimization to maximize your revenue potential.', type: 'text' }\n        ],\n        our_expertise: [\n          { section: 'our_expertise', key: 'title', value: 'Our Expertise', type: 'text' },\n          { section: 'our_expertise', key: 'description', value: 'Deep knowledge across all major ad platforms and technologies.', type: 'text' },\n          { section: 'our_expertise', key: 'expertise_1_title', value: 'Programmatic Advertising', type: 'text' },\n          { section: 'our_expertise', key: 'expertise_1_description', value: 'Advanced programmatic strategies and real-time bidding optimization.', type: 'text' },\n          { section: 'our_expertise', key: 'expertise_1_icon', value: '', type: 'image' },\n          { section: 'our_expertise', key: 'expertise_2_title', value: 'Header Bidding', type: 'text' },\n          { section: 'our_expertise', key: 'expertise_2_description', value: 'Implementation and optimization of header bidding solutions.', type: 'text' },\n          { section: 'our_expertise', key: 'expertise_2_icon', value: '', type: 'image' },\n          { section: 'our_expertise', key: 'expertise_3_title', value: 'Ad Quality & Fraud Prevention', type: 'text' },\n          { section: 'our_expertise', key: 'expertise_3_description', value: 'Comprehensive ad quality control and fraud detection systems.', type: 'text' },\n          { section: 'our_expertise', key: 'expertise_3_icon', value: '', type: 'image' }\n        ],\n        testimonials: [\n          { section: 'testimonials', key: 'title', value: 'What Our Clients Say', type: 'text' },\n          { section: 'testimonials', key: 'description', value: 'Hear from publishers who have transformed their ad revenue with RevAdOps.', type: 'text' },\n          { section: 'testimonials', key: 'testimonial_1_text', value: 'RevAdOps increased our ad revenue by 45% in just 3 months. Their team is incredibly knowledgeable and responsive.', type: 'text' },\n          { section: 'testimonials', key: 'testimonial_1_author', value: 'Sarah Johnson', type: 'text' },\n          { section: 'testimonials', key: 'testimonial_1_company', value: 'TechNews Daily', type: 'text' },\n          { section: 'testimonials', key: 'testimonial_2_text', value: 'The fraud detection capabilities saved us thousands in invalid traffic. Highly recommend their services.', type: 'text' },\n          { section: 'testimonials', key: 'testimonial_2_author', value: 'Mike Chen', type: 'text' },\n          { section: 'testimonials', key: 'testimonial_2_company', value: 'Gaming Hub', type: 'text' },\n          { section: 'testimonials', key: 'testimonial_3_text', value: 'Professional service and outstanding results. Our fill rates improved dramatically.', type: 'text' },\n          { section: 'testimonials', key: 'testimonial_3_author', value: 'Lisa Rodriguez', type: 'text' },\n          { section: 'testimonials', key: 'testimonial_3_company', value: 'Mobile App Co.', type: 'text' }\n        ],\n\n        final_cta: [\n          { section: 'final_cta', key: 'title', value: 'Ready to Maximize Your Ad Revenue?', type: 'text' },\n          { section: 'final_cta', key: 'description', value: 'Join hundreds of publishers who have increased their revenue with RevAdOps. Get started with a free consultation today.', type: 'text' },\n          { section: 'final_cta', key: 'cta_primary_text', value: 'Get Free Consultation', type: 'text' },\n          { section: 'final_cta', key: 'cta_primary_link', value: '/consultation', type: 'text' },\n          { section: 'final_cta', key: 'cta_secondary_text', value: 'Contact Us', type: 'text' },\n          { section: 'final_cta', key: 'cta_secondary_link', value: '/contact', type: 'text' }\n        ],\n        footer: [\n          { section: 'footer', key: 'company_description', value: 'RevAdOps - Your trusted partner in ad revenue optimization and traffic quality management.', type: 'text' },\n          { section: 'footer', key: 'linkedin_link', value: 'https://linkedin.com/company/revadops', type: 'text' },\n          { section: 'footer', key: 'upwork_link', value: 'https://upwork.com/agencies/revadops', type: 'text' },\n          { section: 'footer', key: 'copyright_text', value: '© 2024 RevAdOps. All rights reserved.', type: 'text' }\n        ]\n      };\n\n      // Merge API content with defaults\n      const mergedContent: HomepageContent = {};\n      Object.keys(defaultContent).forEach(section => {\n        mergedContent[section] = defaultContent[section].map(item => ({\n          ...item,\n          value: apiContent[section]?.[item.key] || item.value\n        }));\n      });\n\n      setContent(mergedContent);\n      setOriginalContent(JSON.parse(JSON.stringify(mergedContent))); // Deep copy for comparison\n      setExpertiseItems(expertiseData);\n      setTestimonialItems(testimonialsData);\n    } catch (error) {\n      console.error('Failed to fetch homepage content:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleContentChange = (section: string, key: string, value: string) => {\n    setContent(prev => ({\n      ...prev,\n      [section]: prev[section].map(item =>\n        item.key === key ? { ...item, value } : item\n      )\n    }));\n  };\n\n  // Expertise management functions\n  const addExpertiseItem = () => {\n    const newItem: ExpertiseItem = {\n      title: '',\n      description: '',\n      icon: '',\n      order: expertiseItems.length + 1\n    };\n    setExpertiseItems(prev => [...prev, newItem]);\n  };\n\n  const updateExpertiseItem = (index: number, field: keyof ExpertiseItem, value: string | number) => {\n    setExpertiseItems(prev => prev.map((item, i) =>\n      i === index ? { ...item, [field]: value } : item\n    ));\n  };\n\n  const deleteExpertiseItem = (index: number) => {\n    setExpertiseItems(prev => prev.filter((_, i) => i !== index));\n  };\n\n  // Testimonials management functions\n  const addTestimonialItem = () => {\n    const newItem: TestimonialItem = {\n      text: '',\n      author: '',\n      company: '',\n      avatar: '',\n      order: testimonialItems.length + 1\n    };\n    setTestimonialItems(prev => [...prev, newItem]);\n  };\n\n  const updateTestimonialItem = (index: number, field: keyof TestimonialItem, value: string | number) => {\n    setTestimonialItems(prev => prev.map((item, i) =>\n      i === index ? { ...item, [field]: value } : item\n    ));\n  };\n\n  const deleteTestimonialItem = (index: number) => {\n    setTestimonialItems(prev => prev.filter((_, i) => i !== index));\n  };\n\n  // Handle expertise icon upload\n  const handleExpertiseImageUpload = async (index: number, file: File) => {\n    const uploadKey = `expertise-${index}-icon`;\n    setUploadingImages(prev => ({ ...prev, [uploadKey]: true }));\n\n    try {\n      const formData = new FormData();\n      formData.append('image', file);\n      formData.append('section', 'expertise');\n      formData.append('key', `expertise_${index}_icon`);\n\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/upload/admin/image`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n        },\n        body: formData,\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        updateExpertiseItem(index, 'icon', result.url);\n      } else {\n        console.error('Image upload failed');\n        alert('Image upload failed. Please try again.');\n      }\n    } catch (error) {\n      console.error('Image upload error:', error);\n      alert('Image upload failed. Please try again.');\n    } finally {\n      setUploadingImages(prev => ({ ...prev, [uploadKey]: false }));\n    }\n  };\n\n  // Handle testimonial avatar upload\n  const handleTestimonialImageUpload = async (index: number, file: File) => {\n    const uploadKey = `testimonial-${index}-avatar`;\n    setUploadingImages(prev => ({ ...prev, [uploadKey]: true }));\n\n    try {\n      const formData = new FormData();\n      formData.append('image', file);\n      formData.append('section', 'testimonials');\n      formData.append('key', `testimonial_${index}_avatar`);\n\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/upload/admin/image`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n        },\n        body: formData,\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        updateTestimonialItem(index, 'avatar', result.url);\n      } else {\n        console.error('Image upload failed');\n        alert('Image upload failed. Please try again.');\n      }\n    } catch (error) {\n      console.error('Image upload error:', error);\n      alert('Image upload failed. Please try again.');\n    } finally {\n      setUploadingImages(prev => ({ ...prev, [uploadKey]: false }));\n    }\n  };\n\n  const handleImageUpload = async (section: string, key: string, file: File) => {\n    const uploadKey = `${section}-${key}`;\n    setUploadingImages(prev => ({ ...prev, [uploadKey]: true }));\n\n    try {\n      const formData = new FormData();\n      formData.append('image', file);\n      formData.append('section', section);\n      formData.append('key', key);\n\n      const token = localStorage.getItem('adminToken');\n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/upload/admin/image`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n        },\n        body: formData,\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        handleContentChange(section, key, result.url);\n      } else {\n        console.error('Image upload failed');\n        alert('Image upload failed. Please try again.');\n      }\n    } catch (error) {\n      console.error('Image upload error:', error);\n      alert('Image upload failed. Please try again.');\n    } finally {\n      setUploadingImages(prev => ({ ...prev, [uploadKey]: false }));\n    }\n  };\n\n  const saveContent = async () => {\n    setIsSaving(true);\n    try {\n      const token = localStorage.getItem('adminToken');\n\n      // Save regular content updates\n      const updates: Array<{section: string; key: string; value: string; type: string; metadata?: Record<string, unknown>; order?: number}> = [];\n\n      // Only save items that have changed\n      Object.entries(content).forEach(([section, items]) => {\n        items.forEach(item => {\n          const originalItem = originalContent[section]?.find(orig => orig.key === item.key);\n          if (!originalItem || originalItem.value !== item.value) {\n            updates.push({\n              section: item.section,\n              key: item.key,\n              value: item.value,\n              type: item.type,\n              metadata: item.metadata || {},\n              order: item.order || 0\n            });\n          }\n        });\n      });\n\n      // Save regular content if there are updates\n      if (updates.length > 0) {\n        const contentResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/homepage/bulk`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`,\n          },\n          body: JSON.stringify({ updates }),\n        });\n\n        if (!contentResponse.ok) {\n          const errorData = await contentResponse.json();\n          console.error('Content save error:', errorData);\n          alert('Failed to save content: ' + (errorData.message || 'Unknown error'));\n          return;\n        }\n      }\n\n      // Save expertise items\n      const expertiseResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/expertise/bulk`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`,\n        },\n        body: JSON.stringify({ items: expertiseItems }),\n      });\n\n      if (!expertiseResponse.ok) {\n        const errorData = await expertiseResponse.json();\n        console.error('Expertise save error:', errorData);\n        alert('Failed to save expertise: ' + (errorData.message || 'Unknown error'));\n        return;\n      }\n\n      // Save testimonials\n      const testimonialsResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/admin/testimonials/bulk`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`,\n        },\n        body: JSON.stringify({ items: testimonialItems }),\n      });\n\n      if (!testimonialsResponse.ok) {\n        const errorData = await testimonialsResponse.json();\n        console.error('Testimonials save error:', errorData);\n        alert('Failed to save testimonials: ' + (errorData.message || 'Unknown error'));\n        return;\n      }\n\n      alert('All content saved successfully!');\n      await fetchHomepageContent();\n    } catch (error) {\n      console.error('Save error:', error);\n      alert('Failed to save content. Please try again.');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const sections = [\n    { id: 'header', name: 'Header', icon: Type },\n    { id: 'hero', name: 'Hero Section', icon: Type },\n    { id: 'what_we_do', name: 'What We Do', icon: Type },\n    { id: 'why_choose_us', name: 'Why Choose Us', icon: Type },\n    { id: 'how_it_works', name: 'How It Works', icon: Type },\n    { id: 'our_expertise', name: 'Our Expertise', icon: Target },\n    { id: 'testimonials', name: 'Testimonials', icon: MessageSquare },\n    { id: 'final_cta', name: 'Final CTA', icon: Type },\n    { id: 'footer', name: 'Footer & Social', icon: Type }\n  ];\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Homepage Content</h1>\n          <p className=\"text-gray-600\">Manage your homepage content (images are now static)</p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <a\n            href=\"/\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            <Eye className=\"h-4 w-4 mr-2\" />\n            Preview\n          </a>\n          <button\n            onClick={saveContent}\n            disabled={isSaving}\n            className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\"\n          >\n            <Save className=\"h-4 w-4 mr-2\" />\n            {isSaving ? 'Saving...' : 'Save Changes'}\n          </button>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n        {/* Sidebar */}\n        <div className=\"lg:col-span-1\">\n          <nav className=\"space-y-1\">\n            {sections.map((section) => {\n              const Icon = section.icon;\n              return (\n                <button\n                  key={section.id}\n                  onClick={() => setActiveSection(section.id)}\n                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${\n                    activeSection === section.id\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                  }`}\n                >\n                  <Icon className=\"h-4 w-4 mr-3\" />\n                  {section.name}\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n\n        {/* Content */}\n        <div className=\"lg:col-span-3\">\n          <div className=\"bg-white shadow rounded-lg p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n              {sections.find(s => s.id === activeSection)?.name}\n            </h3>\n\n            {/* Special handling for Our Expertise section */}\n            {activeSection === 'our_expertise' ? (\n              <div className=\"space-y-6\">\n                {/* Section Title and Description */}\n                {content.our_expertise?.filter(item => item.key === 'title' || item.key === 'description').map((item) => (\n                  <div key={`${item.section}-${item.key}`} className=\"space-y-2\">\n                    <label className=\"block text-sm font-medium text-gray-700 capitalize\">\n                      {item.key.replace(/_/g, ' ')}\n                    </label>\n                    {item.key.includes('description') ? (\n                      <textarea\n                        value={item.value}\n                        onChange={(e) => handleContentChange(item.section, item.key, e.target.value)}\n                        rows={3}\n                        className=\"block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white\"\n                        placeholder={`Enter ${item.key.replace(/_/g, ' ')}`}\n                      />\n                    ) : (\n                      <input\n                        type=\"text\"\n                        value={item.value}\n                        onChange={(e) => handleContentChange(item.section, item.key, e.target.value)}\n                        className=\"block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white\"\n                        placeholder={`Enter ${item.key.replace(/_/g, ' ')}`}\n                      />\n                    )}\n                  </div>\n                ))}\n\n                {/* Dynamic Expertise Items */}\n                <div className=\"border-t pt-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h4 className=\"text-md font-medium text-gray-900\">Expertise Items</h4>\n                    <button\n                      onClick={addExpertiseItem}\n                      className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n                    >\n                      <Plus className=\"h-4 w-4 mr-1\" />\n                      Add New\n                    </button>\n                  </div>\n\n                  <div className=\"space-y-4\">\n                    {expertiseItems.map((item, index) => (\n                      <div key={index} className=\"border border-gray-200 rounded-lg p-4 space-y-3\">\n                        <div className=\"flex items-center justify-between\">\n                          <h5 className=\"text-sm font-medium text-gray-700\">Expertise Item {index + 1}</h5>\n                          <button\n                            onClick={() => deleteExpertiseItem(index)}\n                            className=\"text-red-600 hover:text-red-800\"\n                          >\n                            <Trash2 className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n\n                        <div className=\"grid grid-cols-1 gap-3\">\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Title</label>\n                            <input\n                              type=\"text\"\n                              value={item.title}\n                              onChange={(e) => updateExpertiseItem(index, 'title', e.target.value)}\n                              className=\"mt-1 block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white\"\n                              placeholder=\"Enter expertise title\"\n                            />\n                          </div>\n\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Description</label>\n                            <textarea\n                              value={item.description}\n                              onChange={(e) => updateExpertiseItem(index, 'description', e.target.value)}\n                              rows={2}\n                              className=\"mt-1 block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white\"\n                              placeholder=\"Enter expertise description\"\n                            />\n                          </div>\n\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Icon</label>\n\n                            {/* Current Icon Preview */}\n                            {item.icon && (\n                              <div className=\"mt-2 mb-3\">\n                                <div className=\"relative w-16 h-16 border border-gray-300 rounded-md overflow-hidden bg-gray-50\">\n                                  <Image\n                                    src={item.icon}\n                                    alt=\"Expertise icon\"\n                                    fill\n                                    className=\"object-contain\"\n                                  />\n                                </div>\n                              </div>\n                            )}\n\n                            {/* Upload and URL Input */}\n                            <div className=\"space-y-3\">\n                              <div className=\"flex items-center space-x-3\">\n                                <label className=\"cursor-pointer inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                                  <Upload className=\"h-4 w-4 mr-2\" />\n                                  {uploadingImages[`expertise-${index}-icon`] ? 'Uploading...' : 'Upload Icon'}\n                                  <input\n                                    type=\"file\"\n                                    accept=\"image/*\"\n                                    className=\"hidden\"\n                                    onChange={(e) => {\n                                      const file = e.target.files?.[0];\n                                      if (file) {\n                                        handleExpertiseImageUpload(index, file);\n                                      }\n                                    }}\n                                    disabled={uploadingImages[`expertise-${index}-icon`]}\n                                  />\n                                </label>\n                                <span className=\"text-sm text-gray-500\">or</span>\n                              </div>\n\n                              <input\n                                type=\"url\"\n                                value={item.icon}\n                                onChange={(e) => updateExpertiseItem(index, 'icon', e.target.value)}\n                                className=\"block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white\"\n                                placeholder=\"Enter icon URL (64x64 pixels recommended)\"\n                              />\n\n                              <p className=\"text-xs text-gray-500\">\n                                Recommended: 64x64 pixels, PNG with transparent background, under 50KB\n                              </p>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            ) : activeSection === 'testimonials' ? (\n              <div className=\"space-y-6\">\n                {/* Section Title and Description */}\n                {content.testimonials?.filter(item => item.key === 'title' || item.key === 'description').map((item) => (\n                  <div key={`${item.section}-${item.key}`} className=\"space-y-2\">\n                    <label className=\"block text-sm font-medium text-gray-700 capitalize\">\n                      {item.key.replace(/_/g, ' ')}\n                    </label>\n                    {item.key.includes('description') ? (\n                      <textarea\n                        value={item.value}\n                        onChange={(e) => handleContentChange(item.section, item.key, e.target.value)}\n                        rows={3}\n                        className=\"block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white\"\n                        placeholder={`Enter ${item.key.replace(/_/g, ' ')}`}\n                      />\n                    ) : (\n                      <input\n                        type=\"text\"\n                        value={item.value}\n                        onChange={(e) => handleContentChange(item.section, item.key, e.target.value)}\n                        className=\"block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white\"\n                        placeholder={`Enter ${item.key.replace(/_/g, ' ')}`}\n                      />\n                    )}\n                  </div>\n                ))}\n\n                {/* Dynamic Testimonial Items */}\n                <div className=\"border-t pt-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h4 className=\"text-md font-medium text-gray-900\">Testimonial Items</h4>\n                    <button\n                      onClick={addTestimonialItem}\n                      className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n                    >\n                      <Plus className=\"h-4 w-4 mr-1\" />\n                      Add New\n                    </button>\n                  </div>\n\n                  <div className=\"space-y-4\">\n                    {testimonialItems.map((item, index) => (\n                      <div key={index} className=\"border border-gray-200 rounded-lg p-4 space-y-3\">\n                        <div className=\"flex items-center justify-between\">\n                          <h5 className=\"text-sm font-medium text-gray-700\">Testimonial {index + 1}</h5>\n                          <button\n                            onClick={() => deleteTestimonialItem(index)}\n                            className=\"text-red-600 hover:text-red-800\"\n                          >\n                            <Trash2 className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n\n                        <div className=\"grid grid-cols-1 gap-3\">\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Testimonial Text</label>\n                            <textarea\n                              value={item.text}\n                              onChange={(e) => updateTestimonialItem(index, 'text', e.target.value)}\n                              rows={3}\n                              className=\"mt-1 block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white\"\n                              placeholder=\"Enter testimonial text\"\n                            />\n                          </div>\n\n                          <div className=\"grid grid-cols-2 gap-3\">\n                            <div>\n                              <label className=\"block text-sm font-medium text-gray-700\">Author Name</label>\n                              <input\n                                type=\"text\"\n                                value={item.author}\n                                onChange={(e) => updateTestimonialItem(index, 'author', e.target.value)}\n                                className=\"mt-1 block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white\"\n                                placeholder=\"Enter author name\"\n                              />\n                            </div>\n\n                            <div>\n                              <label className=\"block text-sm font-medium text-gray-700\">Company</label>\n                              <input\n                                type=\"text\"\n                                value={item.company}\n                                onChange={(e) => updateTestimonialItem(index, 'company', e.target.value)}\n                                className=\"mt-1 block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white\"\n                                placeholder=\"Enter company name\"\n                              />\n                            </div>\n                          </div>\n\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700\">Author Avatar (Optional)</label>\n\n                            {/* Current Avatar Preview */}\n                            {item.avatar && (\n                              <div className=\"mt-2 mb-3\">\n                                <div className=\"relative w-16 h-16 border border-gray-300 rounded-full overflow-hidden bg-gray-50\">\n                                  <Image\n                                    src={item.avatar}\n                                    alt=\"Author avatar\"\n                                    fill\n                                    className=\"object-cover\"\n                                  />\n                                </div>\n                              </div>\n                            )}\n\n                            {/* Upload and URL Input */}\n                            <div className=\"space-y-3\">\n                              <div className=\"flex items-center space-x-3\">\n                                <label className=\"cursor-pointer inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                                  <Upload className=\"h-4 w-4 mr-2\" />\n                                  {uploadingImages[`testimonial-${index}-avatar`] ? 'Uploading...' : 'Upload Avatar'}\n                                  <input\n                                    type=\"file\"\n                                    accept=\"image/*\"\n                                    className=\"hidden\"\n                                    onChange={(e) => {\n                                      const file = e.target.files?.[0];\n                                      if (file) {\n                                        handleTestimonialImageUpload(index, file);\n                                      }\n                                    }}\n                                    disabled={uploadingImages[`testimonial-${index}-avatar`]}\n                                  />\n                                </label>\n                                <span className=\"text-sm text-gray-500\">or</span>\n                              </div>\n\n                              <input\n                                type=\"url\"\n                                value={item.avatar || ''}\n                                onChange={(e) => updateTestimonialItem(index, 'avatar', e.target.value)}\n                                className=\"block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white\"\n                                placeholder=\"Enter avatar URL (optional)\"\n                              />\n\n                              <p className=\"text-xs text-gray-500\">\n                                Recommended: 100x100 pixels, square image, under 100KB\n                              </p>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            ) : (\n              /* Regular content sections */\n              <div className=\"space-y-6\">\n                {content[activeSection as keyof HomepageContent]?.map((item) => (\n                  <div key={`${item.section}-${item.key}`} className=\"space-y-2\">\n                    <label className=\"block text-sm font-medium text-gray-700 capitalize\">\n                      {item.key.replace(/_/g, ' ')}\n                    </label>\n\n                    {item.type === 'image' ? (\n                      <div className=\"space-y-3\">\n                        {/* Current Image Preview */}\n                        {item.value && (\n                          <div className=\"relative w-full h-48 border border-gray-300 rounded-md overflow-hidden\">\n                            <Image\n                              src={item.value}\n                              alt={item.key.replace(/_/g, ' ')}\n                              fill\n                              className=\"object-cover\"\n                            />\n                          </div>\n                        )}\n\n                        {/* Image Upload */}\n                        <div className=\"flex items-center space-x-3\">\n                          <label className=\"cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\">\n                            <Upload className=\"h-4 w-4 mr-2\" />\n                            {uploadingImages[`${item.section}-${item.key}`] ? 'Uploading...' : 'Upload Image'}\n                            <input\n                              type=\"file\"\n                              accept=\"image/*\"\n                              className=\"hidden\"\n                              onChange={(e) => {\n                                const file = e.target.files?.[0];\n                                if (file) {\n                                  handleImageUpload(item.section, item.key, file);\n                                }\n                              }}\n                              disabled={uploadingImages[`${item.section}-${item.key}`]}\n                            />\n                          </label>\n\n                          {/* Image URL Input */}\n                          <input\n                            type=\"url\"\n                            value={item.value}\n                            onChange={(e) => handleContentChange(item.section, item.key, e.target.value)}\n                            className=\"flex-1 border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white\"\n                            placeholder=\"Or enter image URL\"\n                          />\n                        </div>\n\n                        {/* Recommended Dimensions */}\n                        {item.key === 'background_image' && (\n                          <p className=\"text-xs text-gray-500\">\n                            Recommended: 1920x1080 pixels (16:9 aspect ratio), under 500KB\n                          </p>\n                        )}\n                        {item.key === 'logo' && (\n                          <p className=\"text-xs text-gray-500\">\n                            Recommended: 300x100 pixels (3:1 aspect ratio), PNG with transparent background, under 100KB\n                          </p>\n                        )}\n                      </div>\n                    ) : item.key.includes('description') || item.key.includes('subtitle') ? (\n                      <textarea\n                        value={item.value}\n                        onChange={(e) => handleContentChange(item.section, item.key, e.target.value)}\n                        rows={3}\n                        className=\"block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white\"\n                        placeholder={`Enter ${item.key.replace(/_/g, ' ')}`}\n                      />\n                    ) : (\n                      <input\n                        type=\"text\"\n                        value={item.value}\n                        onChange={(e) => handleContentChange(item.section, item.key, e.target.value)}\n                        className=\"block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white\"\n                        placeholder={`Enter ${item.key.replace(/_/g, ' ')}`}\n                      />\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AAkE6C;;AAhE7C;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAqCe,SAAS;QA8eT,gBAOE,wBAuIA,uBAoJA;;IA/wBf,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QACtD,QAAQ,EAAE;QACV,MAAM,EAAE;QACR,YAAY,EAAE;QACd,eAAe,EAAE;QACjB,cAAc,EAAE;QAChB,eAAe,EAAE;QACjB,cAAc,EAAE;QAChB,WAAW,EAAE;QACb,QAAQ,EAAE;IACZ;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,CAAC;IACzE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAEjF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,wBAAwB;YACxB,MAAM,kBAAkB,MAAM,MAAM,AAAC,GAAkC,sEAAA,aAAW;gBAChF,SAAS;oBAAE,iBAAiB,AAAC,UAAe,OAAN;gBAAQ;YAChD;YAEA,IAAI,aAAqD,CAAC;YAC1D,IAAI,gBAAgB,EAAE,EAAE;gBACtB,aAAa,MAAM,gBAAgB,IAAI;YACzC;YAEA,wBAAwB;YACxB,MAAM,oBAAoB,MAAM,MAAM,AAAC,GAAkC,sEAAA,qBAAmB;gBAC1F,SAAS;oBAAE,iBAAiB,AAAC,UAAe,OAAN;gBAAQ;YAChD;YAEA,IAAI,gBAAiC,EAAE;YACvC,IAAI,kBAAkB,EAAE,EAAE;gBACxB,MAAM,kBAAkB,MAAM,kBAAkB,IAAI;gBACpD,gBAAgB,gBAAgB,KAAK,IAAI,EAAE;YAC7C;YAEA,qBAAqB;YACrB,MAAM,uBAAuB,MAAM,MAAM,AAAC,GAAkC,sEAAA,wBAAsB;gBAChG,SAAS;oBAAE,iBAAiB,AAAC,UAAe,OAAN;gBAAQ;YAChD;YAEA,IAAI,mBAAsC,EAAE;YAC5C,IAAI,qBAAqB,EAAE,EAAE;gBAC3B,MAAM,qBAAqB,MAAM,qBAAqB,IAAI;gBAC1D,mBAAmB,mBAAmB,KAAK,IAAI,EAAE;YACnD;YAEA,mDAAmD;YACnD,MAAM,iBAAkC;gBACtC,QAAQ;oBACN;wBAAE,SAAS;wBAAU,KAAK;wBAAgB,OAAO;wBAAY,MAAM;oBAAO;iBAC3E;gBACD,MAAM;oBACJ;wBAAE,SAAS;wBAAQ,KAAK;wBAAS,OAAO;wBAAmE,MAAM;oBAAO;oBACxH;wBAAE,SAAS;wBAAQ,KAAK;wBAAY,OAAO;wBAA2L,MAAM;oBAAO;oBACnP;wBAAE,SAAS;wBAAQ,KAAK;wBAAoB,OAAO;wBAA0K,MAAM;oBAAQ;oBAC3O;wBAAE,SAAS;wBAAQ,KAAK;wBAAoB,OAAO;wBAA2B,MAAM;oBAAO;oBAC3F;wBAAE,SAAS;wBAAQ,KAAK;wBAAoB,OAAO;wBAAiB,MAAM;oBAAO;oBACjF;wBAAE,SAAS;wBAAQ,KAAK;wBAAsB,OAAO;wBAAyB,MAAM;oBAAO;oBAC3F;wBAAE,SAAS;wBAAQ,KAAK;wBAAsB,OAAO;wBAAc,MAAM;oBAAO;iBACjF;gBACD,YAAY;oBACV;wBAAE,SAAS;wBAAc,KAAK;wBAAS,OAAO;wBAA2C,MAAM;oBAAO;oBACtG;wBAAE,SAAS;wBAAc,KAAK;wBAAe,OAAO;wBAAkH,MAAM;oBAAO;oBACnL;wBAAE,SAAS;wBAAc,KAAK;wBAAmB,OAAO;wBAAwB,MAAM;oBAAO;oBAC7F;wBAAE,SAAS;wBAAc,KAAK;wBAAyB,OAAO;wBAA+F,MAAM;oBAAO;oBAC1K;wBAAE,SAAS;wBAAc,KAAK;wBAAmB,OAAO;wBAAsB,MAAM;oBAAO;oBAC3F;wBAAE,SAAS;wBAAc,KAAK;wBAAyB,OAAO;wBAA+F,MAAM;oBAAO;oBAC1K;wBAAE,SAAS;wBAAc,KAAK;wBAAmB,OAAO;wBAAyB,MAAM;oBAAO;oBAC9F;wBAAE,SAAS;wBAAc,KAAK;wBAAyB,OAAO;wBAAiG,MAAM;oBAAO;oBAC5K;wBAAE,SAAS;wBAAc,KAAK;wBAAmB,OAAO;wBAAsB,MAAM;oBAAO;oBAC3F;wBAAE,SAAS;wBAAc,KAAK;wBAAyB,OAAO;wBAAyG,MAAM;oBAAO;iBACrL;gBACD,eAAe;oBACb;wBAAE,SAAS;wBAAiB,KAAK;wBAAS,OAAO;wBAAwB,MAAM;oBAAO;oBACtF;wBAAE,SAAS;wBAAiB,KAAK;wBAAe,OAAO;wBAA8G,MAAM;oBAAO;oBAClL;wBAAE,SAAS;wBAAiB,KAAK;wBAAkB,OAAO;wBAAkB,MAAM;oBAAO;oBACzF;wBAAE,SAAS;wBAAiB,KAAK;wBAAwB,OAAO;wBAAkE,MAAM;oBAAO;oBAC/I;wBAAE,SAAS;wBAAiB,KAAK;wBAAkB,OAAO;wBAAe,MAAM;oBAAO;oBACtF;wBAAE,SAAS;wBAAiB,KAAK;wBAAwB,OAAO;wBAAuE,MAAM;oBAAO;oBACpJ;wBAAE,SAAS;wBAAiB,KAAK;wBAAkB,OAAO;wBAAiB,MAAM;oBAAO;oBACxF;wBAAE,SAAS;wBAAiB,KAAK;wBAAwB,OAAO;wBAAyE,MAAM;oBAAO;oBACtJ;wBAAE,SAAS;wBAAiB,KAAK;wBAAkB,OAAO;wBAAgB,MAAM;oBAAO;oBACvF;wBAAE,SAAS;wBAAiB,KAAK;wBAAwB,OAAO;wBAAyE,MAAM;oBAAO;iBACvJ;gBACD,cAAc;oBACZ;wBAAE,SAAS;wBAAgB,KAAK;wBAAS,OAAO;wBAAgB,MAAM;oBAAO;oBAC7E;wBAAE,SAAS;wBAAgB,KAAK;wBAAe,OAAO;wBAA+E,MAAM;oBAAO;oBAClJ;wBAAE,SAAS;wBAAgB,KAAK;wBAAgB,OAAO;wBAAY,MAAM;oBAAO;oBAChF;wBAAE,SAAS;wBAAgB,KAAK;wBAAsB,OAAO;wBAA6E,MAAM;oBAAO;oBACvJ;wBAAE,SAAS;wBAAgB,KAAK;wBAAgB,OAAO;wBAAY,MAAM;oBAAO;oBAChF;wBAAE,SAAS;wBAAgB,KAAK;wBAAsB,OAAO;wBAA4E,MAAM;oBAAO;oBACtJ;wBAAE,SAAS;wBAAgB,KAAK;wBAAgB,OAAO;wBAAkB,MAAM;oBAAO;oBACtF;wBAAE,SAAS;wBAAgB,KAAK;wBAAsB,OAAO;wBAA4E,MAAM;oBAAO;oBACtJ;wBAAE,SAAS;wBAAgB,KAAK;wBAAgB,OAAO;wBAAgB,MAAM;oBAAO;oBACpF;wBAAE,SAAS;wBAAgB,KAAK;wBAAsB,OAAO;wBAA8E,MAAM;oBAAO;iBACzJ;gBACD,eAAe;oBACb;wBAAE,SAAS;wBAAiB,KAAK;wBAAS,OAAO;wBAAiB,MAAM;oBAAO;oBAC/E;wBAAE,SAAS;wBAAiB,KAAK;wBAAe,OAAO;wBAAkE,MAAM;oBAAO;oBACtI;wBAAE,SAAS;wBAAiB,KAAK;wBAAqB,OAAO;wBAA4B,MAAM;oBAAO;oBACtG;wBAAE,SAAS;wBAAiB,KAAK;wBAA2B,OAAO;wBAAwE,MAAM;oBAAO;oBACxJ;wBAAE,SAAS;wBAAiB,KAAK;wBAAoB,OAAO;wBAAI,MAAM;oBAAQ;oBAC9E;wBAAE,SAAS;wBAAiB,KAAK;wBAAqB,OAAO;wBAAkB,MAAM;oBAAO;oBAC5F;wBAAE,SAAS;wBAAiB,KAAK;wBAA2B,OAAO;wBAAgE,MAAM;oBAAO;oBAChJ;wBAAE,SAAS;wBAAiB,KAAK;wBAAoB,OAAO;wBAAI,MAAM;oBAAQ;oBAC9E;wBAAE,SAAS;wBAAiB,KAAK;wBAAqB,OAAO;wBAAiC,MAAM;oBAAO;oBAC3G;wBAAE,SAAS;wBAAiB,KAAK;wBAA2B,OAAO;wBAAiE,MAAM;oBAAO;oBACjJ;wBAAE,SAAS;wBAAiB,KAAK;wBAAoB,OAAO;wBAAI,MAAM;oBAAQ;iBAC/E;gBACD,cAAc;oBACZ;wBAAE,SAAS;wBAAgB,KAAK;wBAAS,OAAO;wBAAwB,MAAM;oBAAO;oBACrF;wBAAE,SAAS;wBAAgB,KAAK;wBAAe,OAAO;wBAA6E,MAAM;oBAAO;oBAChJ;wBAAE,SAAS;wBAAgB,KAAK;wBAAsB,OAAO;wBAAqH,MAAM;oBAAO;oBAC/L;wBAAE,SAAS;wBAAgB,KAAK;wBAAwB,OAAO;wBAAiB,MAAM;oBAAO;oBAC7F;wBAAE,SAAS;wBAAgB,KAAK;wBAAyB,OAAO;wBAAkB,MAAM;oBAAO;oBAC/F;wBAAE,SAAS;wBAAgB,KAAK;wBAAsB,OAAO;wBAA4G,MAAM;oBAAO;oBACtL;wBAAE,SAAS;wBAAgB,KAAK;wBAAwB,OAAO;wBAAa,MAAM;oBAAO;oBACzF;wBAAE,SAAS;wBAAgB,KAAK;wBAAyB,OAAO;wBAAc,MAAM;oBAAO;oBAC3F;wBAAE,SAAS;wBAAgB,KAAK;wBAAsB,OAAO;wBAAuF,MAAM;oBAAO;oBACjK;wBAAE,SAAS;wBAAgB,KAAK;wBAAwB,OAAO;wBAAkB,MAAM;oBAAO;oBAC9F;wBAAE,SAAS;wBAAgB,KAAK;wBAAyB,OAAO;wBAAkB,MAAM;oBAAO;iBAChG;gBAED,WAAW;oBACT;wBAAE,SAAS;wBAAa,KAAK;wBAAS,OAAO;wBAAsC,MAAM;oBAAO;oBAChG;wBAAE,SAAS;wBAAa,KAAK;wBAAe,OAAO;wBAA2H,MAAM;oBAAO;oBAC3L;wBAAE,SAAS;wBAAa,KAAK;wBAAoB,OAAO;wBAAyB,MAAM;oBAAO;oBAC9F;wBAAE,SAAS;wBAAa,KAAK;wBAAoB,OAAO;wBAAiB,MAAM;oBAAO;oBACtF;wBAAE,SAAS;wBAAa,KAAK;wBAAsB,OAAO;wBAAc,MAAM;oBAAO;oBACrF;wBAAE,SAAS;wBAAa,KAAK;wBAAsB,OAAO;wBAAY,MAAM;oBAAO;iBACpF;gBACD,QAAQ;oBACN;wBAAE,SAAS;wBAAU,KAAK;wBAAuB,OAAO;wBAA8F,MAAM;oBAAO;oBACnK;wBAAE,SAAS;wBAAU,KAAK;wBAAiB,OAAO;wBAAyC,MAAM;oBAAO;oBACxG;wBAAE,SAAS;wBAAU,KAAK;wBAAe,OAAO;wBAAwC,MAAM;oBAAO;oBACrG;wBAAE,SAAS;wBAAU,KAAK;wBAAkB,OAAO;wBAAyC,MAAM;oBAAO;iBAC1G;YACH;YAEA,kCAAkC;YAClC,MAAM,gBAAiC,CAAC;YACxC,OAAO,IAAI,CAAC,gBAAgB,OAAO,CAAC,CAAA;gBAClC,aAAa,CAAC,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;wBAE5C;2BAFqD;wBAC5D,GAAG,IAAI;wBACP,OAAO,EAAA,sBAAA,UAAU,CAAC,QAAQ,cAAnB,0CAAA,mBAAqB,CAAC,KAAK,GAAG,CAAC,KAAI,KAAK,KAAK;oBACtD;;YACF;YAEA,WAAW;YACX,mBAAmB,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,kBAAkB,2BAA2B;YAC1F,kBAAkB;YAClB,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB,CAAC,SAAiB,KAAa;QACzD,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA,OAC3B,KAAK,GAAG,KAAK,MAAM;wBAAE,GAAG,IAAI;wBAAE;oBAAM,IAAI;YAE5C,CAAC;IACH;IAEA,iCAAiC;IACjC,MAAM,mBAAmB;QACvB,MAAM,UAAyB;YAC7B,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO,eAAe,MAAM,GAAG;QACjC;QACA,kBAAkB,CAAA,OAAQ;mBAAI;gBAAM;aAAQ;IAC9C;IAEA,MAAM,sBAAsB,CAAC,OAAe,OAA4B;QACtE,kBAAkB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAC,MAAM,IACxC,MAAM,QAAQ;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAM,IAAI;IAEhD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACxD;IAEA,oCAAoC;IACpC,MAAM,qBAAqB;QACzB,MAAM,UAA2B;YAC/B,MAAM;YACN,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,OAAO,iBAAiB,MAAM,GAAG;QACnC;QACA,oBAAoB,CAAA,OAAQ;mBAAI;gBAAM;aAAQ;IAChD;IAEA,MAAM,wBAAwB,CAAC,OAAe,OAA8B;QAC1E,oBAAoB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAC,MAAM,IAC1C,MAAM,QAAQ;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAM,IAAI;IAEhD;IAEA,MAAM,wBAAwB,CAAC;QAC7B,oBAAoB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IAC1D;IAEA,+BAA+B;IAC/B,MAAM,6BAA6B,OAAO,OAAe;QACvD,MAAM,YAAY,AAAC,aAAkB,OAAN,OAAM;QACrC,mBAAmB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,UAAU,EAAE;YAAK,CAAC;QAE1D,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS;YACzB,SAAS,MAAM,CAAC,WAAW;YAC3B,SAAS,MAAM,CAAC,OAAO,AAAC,aAAkB,OAAN,OAAM;YAE1C,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,AAAC,GAAkC,sEAAA,wBAAsB;gBACpF,QAAQ;gBACR,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;gBACA,MAAM;YACR;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,oBAAoB,OAAO,QAAQ,OAAO,GAAG;YAC/C,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR,SAAU;YACR,mBAAmB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,UAAU,EAAE;gBAAM,CAAC;QAC7D;IACF;IAEA,mCAAmC;IACnC,MAAM,+BAA+B,OAAO,OAAe;QACzD,MAAM,YAAY,AAAC,eAAoB,OAAN,OAAM;QACvC,mBAAmB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,UAAU,EAAE;YAAK,CAAC;QAE1D,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS;YACzB,SAAS,MAAM,CAAC,WAAW;YAC3B,SAAS,MAAM,CAAC,OAAO,AAAC,eAAoB,OAAN,OAAM;YAE5C,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,AAAC,GAAkC,sEAAA,wBAAsB;gBACpF,QAAQ;gBACR,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;gBACA,MAAM;YACR;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,sBAAsB,OAAO,UAAU,OAAO,GAAG;YACnD,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR,SAAU;YACR,mBAAmB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,UAAU,EAAE;gBAAM,CAAC;QAC7D;IACF;IAEA,MAAM,oBAAoB,OAAO,SAAiB,KAAa;QAC7D,MAAM,YAAY,AAAC,GAAa,OAAX,SAAQ,KAAO,OAAJ;QAChC,mBAAmB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,UAAU,EAAE;YAAK,CAAC;QAE1D,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS;YACzB,SAAS,MAAM,CAAC,WAAW;YAC3B,SAAS,MAAM,CAAC,OAAO;YAEvB,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,AAAC,GAAkC,sEAAA,wBAAsB;gBACpF,QAAQ;gBACR,SAAS;oBACP,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;gBACA,MAAM;YACR;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,oBAAoB,SAAS,KAAK,OAAO,GAAG;YAC9C,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR,SAAU;YACR,mBAAmB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,UAAU,EAAE;gBAAM,CAAC;QAC7D;IACF;IAEA,MAAM,cAAc;QAClB,YAAY;QACZ,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,+BAA+B;YAC/B,MAAM,UAAkI,EAAE;YAE1I,oCAAoC;YACpC,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC;oBAAC,CAAC,SAAS,MAAM;gBAC/C,MAAM,OAAO,CAAC,CAAA;wBACS;oBAArB,MAAM,gBAAe,2BAAA,eAAe,CAAC,QAAQ,cAAxB,+CAAA,yBAA0B,IAAI,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK,KAAK,GAAG;oBACjF,IAAI,CAAC,gBAAgB,aAAa,KAAK,KAAK,KAAK,KAAK,EAAE;wBACtD,QAAQ,IAAI,CAAC;4BACX,SAAS,KAAK,OAAO;4BACrB,KAAK,KAAK,GAAG;4BACb,OAAO,KAAK,KAAK;4BACjB,MAAM,KAAK,IAAI;4BACf,UAAU,KAAK,QAAQ,IAAI,CAAC;4BAC5B,OAAO,KAAK,KAAK,IAAI;wBACvB;oBACF;gBACF;YACF;YAEA,4CAA4C;YAC5C,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACtB,MAAM,kBAAkB,MAAM,MAAM,AAAC,GAAkC,sEAAA,yBAAuB;oBAC5F,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,iBAAiB,AAAC,UAAe,OAAN;oBAC7B;oBACA,MAAM,KAAK,SAAS,CAAC;wBAAE;oBAAQ;gBACjC;gBAEA,IAAI,CAAC,gBAAgB,EAAE,EAAE;oBACvB,MAAM,YAAY,MAAM,gBAAgB,IAAI;oBAC5C,QAAQ,KAAK,CAAC,uBAAuB;oBACrC,MAAM,6BAA6B,CAAC,UAAU,OAAO,IAAI,eAAe;oBACxE;gBACF;YACF;YAEA,uBAAuB;YACvB,MAAM,oBAAoB,MAAM,MAAM,AAAC,GAAkC,sEAAA,0BAAwB;gBAC/F,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,OAAO;gBAAe;YAC/C;YAEA,IAAI,CAAC,kBAAkB,EAAE,EAAE;gBACzB,MAAM,YAAY,MAAM,kBAAkB,IAAI;gBAC9C,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,MAAM,+BAA+B,CAAC,UAAU,OAAO,IAAI,eAAe;gBAC1E;YACF;YAEA,oBAAoB;YACpB,MAAM,uBAAuB,MAAM,MAAM,AAAC,GAAkC,sEAAA,6BAA2B;gBACrG,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,UAAe,OAAN;gBAC7B;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,OAAO;gBAAiB;YACjD;YAEA,IAAI,CAAC,qBAAqB,EAAE,EAAE;gBAC5B,MAAM,YAAY,MAAM,qBAAqB,IAAI;gBACjD,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,MAAM,kCAAkC,CAAC,UAAU,OAAO,IAAI,eAAe;gBAC7E;YACF;YAEA,MAAM;YACN,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,MAAM;QACR,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,WAAW;QACf;YAAE,IAAI;YAAU,MAAM;YAAU,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC3C;YAAE,IAAI;YAAQ,MAAM;YAAgB,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC/C;YAAE,IAAI;YAAc,MAAM;YAAc,MAAM,qMAAA,CAAA,OAAI;QAAC;QACnD;YAAE,IAAI;YAAiB,MAAM;YAAiB,MAAM,qMAAA,CAAA,OAAI;QAAC;QACzD;YAAE,IAAI;YAAgB,MAAM;YAAgB,MAAM,qMAAA,CAAA,OAAI;QAAC;QACvD;YAAE,IAAI;YAAiB,MAAM;YAAiB,MAAM,yMAAA,CAAA,SAAM;QAAC;QAC3D;YAAE,IAAI;YAAgB,MAAM;YAAgB,MAAM,2NAAA,CAAA,gBAAa;QAAC;QAChE;YAAE,IAAI;YAAa,MAAM;YAAa,MAAM,qMAAA,CAAA,OAAI;QAAC;QACjD;YAAE,IAAI;YAAU,MAAM;YAAmB,MAAM,qMAAA,CAAA,OAAI;QAAC;KACrD;IAED,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;;kDAEV,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGlC,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,WAAW,cAAc;;;;;;;;;;;;;;;;;;;0BAKhC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,OAAO,QAAQ,IAAI;gCACzB,qBACE,6LAAC;oCAEC,SAAS,IAAM,iBAAiB,QAAQ,EAAE;oCAC1C,WAAW,AAAC,qEAIX,OAHC,kBAAkB,QAAQ,EAAE,GACxB,8BACA;;sDAGN,6LAAC;4CAAK,WAAU;;;;;;wCACf,QAAQ,IAAI;;mCATR,QAAQ,EAAE;;;;;4BAYrB;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;+CACX,iBAAA,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,4BAA5B,qCAAA,eAA4C,IAAI;;;;;;gCAIlD,kBAAkB,gCACjB,6LAAC;oCAAI,WAAU;;yCAEZ,yBAAA,QAAQ,aAAa,cAArB,6CAAA,uBAAuB,MAAM,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK,WAAW,KAAK,GAAG,KAAK,eAAe,GAAG,CAAC,CAAC,qBAC9F,6LAAC;gDAAwC,WAAU;;kEACjD,6LAAC;wDAAM,WAAU;kEACd,KAAK,GAAG,CAAC,OAAO,CAAC,MAAM;;;;;;oDAEzB,KAAK,GAAG,CAAC,QAAQ,CAAC,+BACjB,6LAAC;wDACC,OAAO,KAAK,KAAK;wDACjB,UAAU,CAAC,IAAM,oBAAoB,KAAK,OAAO,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;wDAC3E,MAAM;wDACN,WAAU;wDACV,aAAa,AAAC,SAAoC,OAA5B,KAAK,GAAG,CAAC,OAAO,CAAC,MAAM;;;;;6EAG/C,6LAAC;wDACC,MAAK;wDACL,OAAO,KAAK,KAAK;wDACjB,UAAU,CAAC,IAAM,oBAAoB,KAAK,OAAO,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;wDAC3E,WAAU;wDACV,aAAa,AAAC,SAAoC,OAA5B,KAAK,GAAG,CAAC,OAAO,CAAC,MAAM;;;;;;;+CAlBzC,AAAC,GAAkB,OAAhB,KAAK,OAAO,EAAC,KAAY,OAAT,KAAK,GAAG;;;;;sDAyBvC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,6LAAC;4DACC,SAAS;4DACT,WAAU;;8EAEV,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;8DAKrC,6LAAC;oDAAI,WAAU;8DACZ,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;;gFAAoC;gFAAgB,QAAQ;;;;;;;sFAC1E,6LAAC;4EACC,SAAS,IAAM,oBAAoB;4EACnC,WAAU;sFAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;;;;;;8EAItB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FACC,6LAAC;oFAAM,WAAU;8FAA0C;;;;;;8FAC3D,6LAAC;oFACC,MAAK;oFACL,OAAO,KAAK,KAAK;oFACjB,UAAU,CAAC,IAAM,oBAAoB,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK;oFACnE,WAAU;oFACV,aAAY;;;;;;;;;;;;sFAIhB,6LAAC;;8FACC,6LAAC;oFAAM,WAAU;8FAA0C;;;;;;8FAC3D,6LAAC;oFACC,OAAO,KAAK,WAAW;oFACvB,UAAU,CAAC,IAAM,oBAAoB,OAAO,eAAe,EAAE,MAAM,CAAC,KAAK;oFACzE,MAAM;oFACN,WAAU;oFACV,aAAY;;;;;;;;;;;;sFAIhB,6LAAC;;8FACC,6LAAC;oFAAM,WAAU;8FAA0C;;;;;;gFAG1D,KAAK,IAAI,kBACR,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFAAI,WAAU;kGACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4FACJ,KAAK,KAAK,IAAI;4FACd,KAAI;4FACJ,IAAI;4FACJ,WAAU;;;;;;;;;;;;;;;;8FAOlB,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;4FAAI,WAAU;;8GACb,6LAAC;oGAAM,WAAU;;sHACf,6LAAC,yMAAA,CAAA,SAAM;4GAAC,WAAU;;;;;;wGACjB,eAAe,CAAC,AAAC,aAAkB,OAAN,OAAM,SAAO,GAAG,iBAAiB;sHAC/D,6LAAC;4GACC,MAAK;4GACL,QAAO;4GACP,WAAU;4GACV,UAAU,CAAC;oHACI;gHAAb,MAAM,QAAO,kBAAA,EAAE,MAAM,CAAC,KAAK,cAAd,sCAAA,eAAgB,CAAC,EAAE;gHAChC,IAAI,MAAM;oHACR,2BAA2B,OAAO;gHACpC;4GACF;4GACA,UAAU,eAAe,CAAC,AAAC,aAAkB,OAAN,OAAM,SAAO;;;;;;;;;;;;8GAGxD,6LAAC;oGAAK,WAAU;8GAAwB;;;;;;;;;;;;sGAG1C,6LAAC;4FACC,MAAK;4FACL,OAAO,KAAK,IAAI;4FAChB,UAAU,CAAC,IAAM,oBAAoB,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;4FAClE,WAAU;4FACV,aAAY;;;;;;sGAGd,6LAAC;4FAAE,WAAU;sGAAwB;;;;;;;;;;;;;;;;;;;;;;;;;2DAjFnC;;;;;;;;;;;;;;;;;;;;;2CA4FhB,kBAAkB,+BACpB,6LAAC;oCAAI,WAAU;;yCAEZ,wBAAA,QAAQ,YAAY,cAApB,4CAAA,sBAAsB,MAAM,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK,WAAW,KAAK,GAAG,KAAK,eAAe,GAAG,CAAC,CAAC,qBAC7F,6LAAC;gDAAwC,WAAU;;kEACjD,6LAAC;wDAAM,WAAU;kEACd,KAAK,GAAG,CAAC,OAAO,CAAC,MAAM;;;;;;oDAEzB,KAAK,GAAG,CAAC,QAAQ,CAAC,+BACjB,6LAAC;wDACC,OAAO,KAAK,KAAK;wDACjB,UAAU,CAAC,IAAM,oBAAoB,KAAK,OAAO,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;wDAC3E,MAAM;wDACN,WAAU;wDACV,aAAa,AAAC,SAAoC,OAA5B,KAAK,GAAG,CAAC,OAAO,CAAC,MAAM;;;;;6EAG/C,6LAAC;wDACC,MAAK;wDACL,OAAO,KAAK,KAAK;wDACjB,UAAU,CAAC,IAAM,oBAAoB,KAAK,OAAO,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;wDAC3E,WAAU;wDACV,aAAa,AAAC,SAAoC,OAA5B,KAAK,GAAG,CAAC,OAAO,CAAC,MAAM;;;;;;;+CAlBzC,AAAC,GAAkB,OAAhB,KAAK,OAAO,EAAC,KAAY,OAAT,KAAK,GAAG;;;;;sDAyBvC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,6LAAC;4DACC,SAAS;4DACT,WAAU;;8EAEV,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;8DAKrC,6LAAC;oDAAI,WAAU;8DACZ,iBAAiB,GAAG,CAAC,CAAC,MAAM,sBAC3B,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;;gFAAoC;gFAAa,QAAQ;;;;;;;sFACvE,6LAAC;4EACC,SAAS,IAAM,sBAAsB;4EACrC,WAAU;sFAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;;;;;;8EAItB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FACC,6LAAC;oFAAM,WAAU;8FAA0C;;;;;;8FAC3D,6LAAC;oFACC,OAAO,KAAK,IAAI;oFAChB,UAAU,CAAC,IAAM,sBAAsB,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;oFACpE,MAAM;oFACN,WAAU;oFACV,aAAY;;;;;;;;;;;;sFAIhB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;;sGACC,6LAAC;4FAAM,WAAU;sGAA0C;;;;;;sGAC3D,6LAAC;4FACC,MAAK;4FACL,OAAO,KAAK,MAAM;4FAClB,UAAU,CAAC,IAAM,sBAAsB,OAAO,UAAU,EAAE,MAAM,CAAC,KAAK;4FACtE,WAAU;4FACV,aAAY;;;;;;;;;;;;8FAIhB,6LAAC;;sGACC,6LAAC;4FAAM,WAAU;sGAA0C;;;;;;sGAC3D,6LAAC;4FACC,MAAK;4FACL,OAAO,KAAK,OAAO;4FACnB,UAAU,CAAC,IAAM,sBAAsB,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK;4FACvE,WAAU;4FACV,aAAY;;;;;;;;;;;;;;;;;;sFAKlB,6LAAC;;8FACC,6LAAC;oFAAM,WAAU;8FAA0C;;;;;;gFAG1D,KAAK,MAAM,kBACV,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFAAI,WAAU;kGACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4FACJ,KAAK,KAAK,MAAM;4FAChB,KAAI;4FACJ,IAAI;4FACJ,WAAU;;;;;;;;;;;;;;;;8FAOlB,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;4FAAI,WAAU;;8GACb,6LAAC;oGAAM,WAAU;;sHACf,6LAAC,yMAAA,CAAA,SAAM;4GAAC,WAAU;;;;;;wGACjB,eAAe,CAAC,AAAC,eAAoB,OAAN,OAAM,WAAS,GAAG,iBAAiB;sHACnE,6LAAC;4GACC,MAAK;4GACL,QAAO;4GACP,WAAU;4GACV,UAAU,CAAC;oHACI;gHAAb,MAAM,QAAO,kBAAA,EAAE,MAAM,CAAC,KAAK,cAAd,sCAAA,eAAgB,CAAC,EAAE;gHAChC,IAAI,MAAM;oHACR,6BAA6B,OAAO;gHACtC;4GACF;4GACA,UAAU,eAAe,CAAC,AAAC,eAAoB,OAAN,OAAM,WAAS;;;;;;;;;;;;8GAG5D,6LAAC;oGAAK,WAAU;8GAAwB;;;;;;;;;;;;sGAG1C,6LAAC;4FACC,MAAK;4FACL,OAAO,KAAK,MAAM,IAAI;4FACtB,UAAU,CAAC,IAAM,sBAAsB,OAAO,UAAU,EAAE,MAAM,CAAC,KAAK;4FACtE,WAAU;4FACV,aAAY;;;;;;sGAGd,6LAAC;4FAAE,WAAU;sGAAwB;;;;;;;;;;;;;;;;;;;;;;;;;2DA9FnC;;;;;;;;;;;;;;;;;;;;;2CA0GlB,4BAA4B,iBAC5B,6LAAC;oCAAI,WAAU;+CACZ,yBAAA,OAAO,CAAC,cAAuC,cAA/C,6CAAA,uBAAiD,GAAG,CAAC,CAAC,qBACrD,6LAAC;4CAAwC,WAAU;;8DACjD,6LAAC;oDAAM,WAAU;8DACd,KAAK,GAAG,CAAC,OAAO,CAAC,MAAM;;;;;;gDAGzB,KAAK,IAAI,KAAK,wBACb,6LAAC;oDAAI,WAAU;;wDAEZ,KAAK,KAAK,kBACT,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gEACJ,KAAK,KAAK,KAAK;gEACf,KAAK,KAAK,GAAG,CAAC,OAAO,CAAC,MAAM;gEAC5B,IAAI;gEACJ,WAAU;;;;;;;;;;;sEAMhB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;;sFACf,6LAAC,yMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEACjB,eAAe,CAAC,AAAC,GAAkB,OAAhB,KAAK,OAAO,EAAC,KAAY,OAAT,KAAK,GAAG,EAAG,GAAG,iBAAiB;sFACnE,6LAAC;4EACC,MAAK;4EACL,QAAO;4EACP,WAAU;4EACV,UAAU,CAAC;oFACI;gFAAb,MAAM,QAAO,kBAAA,EAAE,MAAM,CAAC,KAAK,cAAd,sCAAA,eAAgB,CAAC,EAAE;gFAChC,IAAI,MAAM;oFACR,kBAAkB,KAAK,OAAO,EAAE,KAAK,GAAG,EAAE;gFAC5C;4EACF;4EACA,UAAU,eAAe,CAAC,AAAC,GAAkB,OAAhB,KAAK,OAAO,EAAC,KAAY,OAAT,KAAK,GAAG,EAAG;;;;;;;;;;;;8EAK5D,6LAAC;oEACC,MAAK;oEACL,OAAO,KAAK,KAAK;oEACjB,UAAU,CAAC,IAAM,oBAAoB,KAAK,OAAO,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;oEAC3E,WAAU;oEACV,aAAY;;;;;;;;;;;;wDAKf,KAAK,GAAG,KAAK,oCACZ,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;wDAItC,KAAK,GAAG,KAAK,wBACZ,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;2DAKvC,KAAK,GAAG,CAAC,QAAQ,CAAC,kBAAkB,KAAK,GAAG,CAAC,QAAQ,CAAC,4BACxD,6LAAC;oDACC,OAAO,KAAK,KAAK;oDACjB,UAAU,CAAC,IAAM,oBAAoB,KAAK,OAAO,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;oDAC3E,MAAM;oDACN,WAAU;oDACV,aAAa,AAAC,SAAoC,OAA5B,KAAK,GAAG,CAAC,OAAO,CAAC,MAAM;;;;;yEAG/C,6LAAC;oDACC,MAAK;oDACL,OAAO,KAAK,KAAK;oDACjB,UAAU,CAAC,IAAM,oBAAoB,KAAK,OAAO,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;oDAC3E,WAAU;oDACV,aAAa,AAAC,SAAoC,OAA5B,KAAK,GAAG,CAAC,OAAO,CAAC,MAAM;;;;;;;2CA1EzC,AAAC,GAAkB,OAAhB,KAAK,OAAO,EAAC,KAAY,OAAT,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsFvD;GAv2BwB;KAAA", "debugId": null}}]}