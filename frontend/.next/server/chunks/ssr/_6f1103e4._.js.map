{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata, Viewport } from \"next\";\n// import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from \"next/font/google\";\n// import { <PERSON><PERSON><PERSON> } from \"next/font/google\";\n\nimport \"./globals.css\";\n\n\n// const geistSans = Geist({\n//   variable: \"--font-geist-sans\",\n//   subsets: [\"latin\"],\n// });\n\n\nexport const viewport: Viewport = {\n  width: 'device-width',\n  initialScale: 1,\n};\n\nexport const metadata: Metadata = {\n  title: \"RevAdOps - Unlock Your Ad Revenue Potential with Intelligent Ad Operations\",\n  description: \"RevAdOps helps publishers and app developers maximize revenue, improve fill rates, and maintain healthy traffic quality through advanced AdTech solutions and data-driven optimization.\",\n  keywords: \"ad revenue, programmatic advertising, header bidding, ad monetization, publishers, app developers, adtech\",\n  authors: [{ name: \"RevAdOps Team\" }],\n  robots: \"index, follow\",\n  icons: {\n    icon: [\n      { url: '/favicon.png', sizes: '32x32', type: 'image/png' },\n      { url: '/revadops-logo.png', sizes: '192x192', type: 'image/png' },\n    ],\n    apple: [\n      { url: '/revadops-logo.png', sizes: '180x180', type: 'image/png' },\n    ],\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body\n        // className={`${geistSans.variable} antialiased`}\n      >\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAaO,MAAM,WAAqB;IAChC,OAAO;IACP,cAAc;AAChB;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAgB;KAAE;IACpC,QAAQ;IACR,OAAO;QACL,MAAM;YACJ;gBAAE,KAAK;gBAAgB,OAAO;gBAAS,MAAM;YAAY;YACzD;gBAAE,KAAK;gBAAsB,OAAO;gBAAW,MAAM;YAAY;SAClE;QACD,OAAO;YACL;gBAAE,KAAK;gBAAsB,OAAO;gBAAW,MAAM;YAAY;SAClE;IACH;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;sBAGE;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}