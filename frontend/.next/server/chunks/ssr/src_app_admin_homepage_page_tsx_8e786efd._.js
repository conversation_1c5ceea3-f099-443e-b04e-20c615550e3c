module.exports = {

"[project]/src/app/admin/homepage/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>HomepageAdmin
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Save$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/save.js [app-ssr] (ecmascript) <export default as Save>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/eye.js [app-ssr] (ecmascript) <export default as Eye>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Type$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/type.js [app-ssr] (ecmascript) <export default as Type>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/upload.js [app-ssr] (ecmascript) <export default as Upload>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-ssr] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js [app-ssr] (ecmascript) <export default as Trash2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/target.js [app-ssr] (ecmascript) <export default as Target>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$square$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageSquare$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/message-square.js [app-ssr] (ecmascript) <export default as MessageSquare>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function HomepageAdmin() {
    const [content, setContent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        header: [],
        hero: [],
        what_we_do: [],
        why_choose_us: [],
        how_it_works: [],
        our_expertise: [],
        testimonials: [],
        final_cta: [],
        footer: []
    });
    const [originalContent, setOriginalContent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    const [expertiseItems, setExpertiseItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [testimonialItems, setTestimonialItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [isSaving, setIsSaving] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [activeSection, setActiveSection] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('hero');
    const [uploadingImages, setUploadingImages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        fetchHomepageContent();
    }, []);
    const fetchHomepageContent = async ()=>{
        try {
            const token = localStorage.getItem('adminToken');
            // Fetch regular content
            const contentResponse = await fetch(`${("TURBOPACK compile-time value", "http://localhost:5001/api")}/content`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            let apiContent = {};
            if (contentResponse.ok) {
                apiContent = await contentResponse.json();
            }
            // Fetch expertise items
            const expertiseResponse = await fetch(`${("TURBOPACK compile-time value", "http://localhost:5001/api")}/admin/expertise`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            let expertiseData = [];
            if (expertiseResponse.ok) {
                const expertiseResult = await expertiseResponse.json();
                expertiseData = expertiseResult.items || [];
            }
            // Fetch testimonials
            const testimonialsResponse = await fetch(`${("TURBOPACK compile-time value", "http://localhost:5001/api")}/admin/testimonials`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            let testimonialsData = [];
            if (testimonialsResponse.ok) {
                const testimonialsResult = await testimonialsResponse.json();
                testimonialsData = testimonialsResult.items || [];
            }
            // Default content structure (text only, no images)
            const defaultContent = {
                header: [
                    {
                        section: 'header',
                        key: 'company_name',
                        value: 'RevAdOps',
                        type: 'text'
                    }
                ],
                hero: [
                    {
                        section: 'hero',
                        key: 'title',
                        value: 'Unlock Your Ad Revenue Potential with Intelligent Ad Operations',
                        type: 'text'
                    },
                    {
                        section: 'hero',
                        key: 'subtitle',
                        value: 'RevAdOps helps publishers and app developers maximize revenue, improve fill rates, and maintain healthy traffic quality through advanced AdTech solutions and data-driven optimization.',
                        type: 'text'
                    },
                    {
                        section: 'hero',
                        key: 'background_image',
                        value: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2025&q=80',
                        type: 'image'
                    },
                    {
                        section: 'hero',
                        key: 'cta_primary_text',
                        value: 'Get a Free Consultation',
                        type: 'text'
                    },
                    {
                        section: 'hero',
                        key: 'cta_primary_link',
                        value: '/consultation',
                        type: 'text'
                    },
                    {
                        section: 'hero',
                        key: 'cta_secondary_text',
                        value: 'Explore Our Solutions',
                        type: 'text'
                    },
                    {
                        section: 'hero',
                        key: 'cta_secondary_link',
                        value: '/solutions',
                        type: 'text'
                    }
                ],
                what_we_do: [
                    {
                        section: 'what_we_do',
                        key: 'title',
                        value: 'Your Partner in Smarter Ad Monetization',
                        type: 'text'
                    },
                    {
                        section: 'what_we_do',
                        key: 'description',
                        value: 'At RevAdOps, we specialize in optimizing web, app, and video monetization strategies for publishers worldwide.',
                        type: 'text'
                    },
                    {
                        section: 'what_we_do',
                        key: 'service_1_title',
                        value: 'Revenue Optimization',
                        type: 'text'
                    },
                    {
                        section: 'what_we_do',
                        key: 'service_1_description',
                        value: 'Advanced algorithms and real-time bidding strategies to maximize your ad revenue potential.',
                        type: 'text'
                    },
                    {
                        section: 'what_we_do',
                        key: 'service_2_title',
                        value: 'Ad Quality Control',
                        type: 'text'
                    },
                    {
                        section: 'what_we_do',
                        key: 'service_2_description',
                        value: 'Comprehensive filtering and monitoring to ensure only high-quality ads reach your audience.',
                        type: 'text'
                    },
                    {
                        section: 'what_we_do',
                        key: 'service_3_title',
                        value: 'Performance Analytics',
                        type: 'text'
                    },
                    {
                        section: 'what_we_do',
                        key: 'service_3_description',
                        value: 'Detailed insights and reporting to track performance and identify optimization opportunities.',
                        type: 'text'
                    },
                    {
                        section: 'what_we_do',
                        key: 'service_4_title',
                        value: 'Traffic Protection',
                        type: 'text'
                    },
                    {
                        section: 'what_we_do',
                        key: 'service_4_description',
                        value: 'Advanced fraud detection and prevention to protect your traffic quality and advertiser relationships.',
                        type: 'text'
                    }
                ],
                why_choose_us: [
                    {
                        section: 'why_choose_us',
                        key: 'title',
                        value: 'Why Choose RevAdOps?',
                        type: 'text'
                    },
                    {
                        section: 'why_choose_us',
                        key: 'description',
                        value: 'We combine cutting-edge technology with industry expertise to deliver exceptional results for our clients.',
                        type: 'text'
                    },
                    {
                        section: 'why_choose_us',
                        key: 'reason_1_title',
                        value: 'Proven Results',
                        type: 'text'
                    },
                    {
                        section: 'why_choose_us',
                        key: 'reason_1_description',
                        value: 'Average 40% revenue increase within 90 days of implementation.',
                        type: 'text'
                    },
                    {
                        section: 'why_choose_us',
                        key: 'reason_2_title',
                        value: 'Expert Team',
                        type: 'text'
                    },
                    {
                        section: 'why_choose_us',
                        key: 'reason_2_description',
                        value: 'Dedicated AdTech specialists with 10+ years of industry experience.',
                        type: 'text'
                    },
                    {
                        section: 'why_choose_us',
                        key: 'reason_3_title',
                        value: 'Award-Winning',
                        type: 'text'
                    },
                    {
                        section: 'why_choose_us',
                        key: 'reason_3_description',
                        value: 'Recognized as a leading ad optimization platform by industry experts.',
                        type: 'text'
                    },
                    {
                        section: 'why_choose_us',
                        key: 'reason_4_title',
                        value: '24/7 Support',
                        type: 'text'
                    },
                    {
                        section: 'why_choose_us',
                        key: 'reason_4_description',
                        value: 'Round-the-clock monitoring and support to ensure optimal performance.',
                        type: 'text'
                    }
                ],
                how_it_works: [
                    {
                        section: 'how_it_works',
                        key: 'title',
                        value: 'How It Works',
                        type: 'text'
                    },
                    {
                        section: 'how_it_works',
                        key: 'description',
                        value: 'Our streamlined process ensures quick implementation and immediate results.',
                        type: 'text'
                    },
                    {
                        section: 'how_it_works',
                        key: 'step_1_title',
                        value: 'Analysis',
                        type: 'text'
                    },
                    {
                        section: 'how_it_works',
                        key: 'step_1_description',
                        value: 'We analyze your current ad setup and identify optimization opportunities.',
                        type: 'text'
                    },
                    {
                        section: 'how_it_works',
                        key: 'step_2_title',
                        value: 'Strategy',
                        type: 'text'
                    },
                    {
                        section: 'how_it_works',
                        key: 'step_2_description',
                        value: 'Develop a customized optimization strategy based on your specific needs.',
                        type: 'text'
                    },
                    {
                        section: 'how_it_works',
                        key: 'step_3_title',
                        value: 'Implementation',
                        type: 'text'
                    },
                    {
                        section: 'how_it_works',
                        key: 'step_3_description',
                        value: 'Deploy our solutions with minimal disruption to your current operations.',
                        type: 'text'
                    },
                    {
                        section: 'how_it_works',
                        key: 'step_4_title',
                        value: 'Optimization',
                        type: 'text'
                    },
                    {
                        section: 'how_it_works',
                        key: 'step_4_description',
                        value: 'Continuous monitoring and optimization to maximize your revenue potential.',
                        type: 'text'
                    }
                ],
                our_expertise: [
                    {
                        section: 'our_expertise',
                        key: 'title',
                        value: 'Our Expertise',
                        type: 'text'
                    },
                    {
                        section: 'our_expertise',
                        key: 'description',
                        value: 'Deep knowledge across all major ad platforms and technologies.',
                        type: 'text'
                    },
                    {
                        section: 'our_expertise',
                        key: 'expertise_1_title',
                        value: 'Programmatic Advertising',
                        type: 'text'
                    },
                    {
                        section: 'our_expertise',
                        key: 'expertise_1_description',
                        value: 'Advanced programmatic strategies and real-time bidding optimization.',
                        type: 'text'
                    },
                    {
                        section: 'our_expertise',
                        key: 'expertise_1_icon',
                        value: '',
                        type: 'image'
                    },
                    {
                        section: 'our_expertise',
                        key: 'expertise_2_title',
                        value: 'Header Bidding',
                        type: 'text'
                    },
                    {
                        section: 'our_expertise',
                        key: 'expertise_2_description',
                        value: 'Implementation and optimization of header bidding solutions.',
                        type: 'text'
                    },
                    {
                        section: 'our_expertise',
                        key: 'expertise_2_icon',
                        value: '',
                        type: 'image'
                    },
                    {
                        section: 'our_expertise',
                        key: 'expertise_3_title',
                        value: 'Ad Quality & Fraud Prevention',
                        type: 'text'
                    },
                    {
                        section: 'our_expertise',
                        key: 'expertise_3_description',
                        value: 'Comprehensive ad quality control and fraud detection systems.',
                        type: 'text'
                    },
                    {
                        section: 'our_expertise',
                        key: 'expertise_3_icon',
                        value: '',
                        type: 'image'
                    }
                ],
                testimonials: [
                    {
                        section: 'testimonials',
                        key: 'title',
                        value: 'What Our Clients Say',
                        type: 'text'
                    },
                    {
                        section: 'testimonials',
                        key: 'description',
                        value: 'Hear from publishers who have transformed their ad revenue with RevAdOps.',
                        type: 'text'
                    },
                    {
                        section: 'testimonials',
                        key: 'testimonial_1_text',
                        value: 'RevAdOps increased our ad revenue by 45% in just 3 months. Their team is incredibly knowledgeable and responsive.',
                        type: 'text'
                    },
                    {
                        section: 'testimonials',
                        key: 'testimonial_1_author',
                        value: 'Sarah Johnson',
                        type: 'text'
                    },
                    {
                        section: 'testimonials',
                        key: 'testimonial_1_company',
                        value: 'TechNews Daily',
                        type: 'text'
                    },
                    {
                        section: 'testimonials',
                        key: 'testimonial_2_text',
                        value: 'The fraud detection capabilities saved us thousands in invalid traffic. Highly recommend their services.',
                        type: 'text'
                    },
                    {
                        section: 'testimonials',
                        key: 'testimonial_2_author',
                        value: 'Mike Chen',
                        type: 'text'
                    },
                    {
                        section: 'testimonials',
                        key: 'testimonial_2_company',
                        value: 'Gaming Hub',
                        type: 'text'
                    },
                    {
                        section: 'testimonials',
                        key: 'testimonial_3_text',
                        value: 'Professional service and outstanding results. Our fill rates improved dramatically.',
                        type: 'text'
                    },
                    {
                        section: 'testimonials',
                        key: 'testimonial_3_author',
                        value: 'Lisa Rodriguez',
                        type: 'text'
                    },
                    {
                        section: 'testimonials',
                        key: 'testimonial_3_company',
                        value: 'Mobile App Co.',
                        type: 'text'
                    }
                ],
                final_cta: [
                    {
                        section: 'final_cta',
                        key: 'title',
                        value: 'Ready to Maximize Your Ad Revenue?',
                        type: 'text'
                    },
                    {
                        section: 'final_cta',
                        key: 'description',
                        value: 'Join hundreds of publishers who have increased their revenue with RevAdOps. Get started with a free consultation today.',
                        type: 'text'
                    },
                    {
                        section: 'final_cta',
                        key: 'cta_primary_text',
                        value: 'Get Free Consultation',
                        type: 'text'
                    },
                    {
                        section: 'final_cta',
                        key: 'cta_primary_link',
                        value: '/consultation',
                        type: 'text'
                    },
                    {
                        section: 'final_cta',
                        key: 'cta_secondary_text',
                        value: 'Contact Us',
                        type: 'text'
                    },
                    {
                        section: 'final_cta',
                        key: 'cta_secondary_link',
                        value: '/contact',
                        type: 'text'
                    }
                ],
                footer: [
                    {
                        section: 'footer',
                        key: 'company_description',
                        value: 'RevAdOps - Your trusted partner in ad revenue optimization and traffic quality management.',
                        type: 'text'
                    },
                    {
                        section: 'footer',
                        key: 'linkedin_link',
                        value: 'https://linkedin.com/company/revadops',
                        type: 'text'
                    },
                    {
                        section: 'footer',
                        key: 'upwork_link',
                        value: 'https://upwork.com/agencies/revadops',
                        type: 'text'
                    },
                    {
                        section: 'footer',
                        key: 'copyright_text',
                        value: '© 2024 RevAdOps. All rights reserved.',
                        type: 'text'
                    }
                ]
            };
            // Merge API content with defaults
            const mergedContent = {};
            Object.keys(defaultContent).forEach((section)=>{
                mergedContent[section] = defaultContent[section].map((item)=>({
                        ...item,
                        value: apiContent[section]?.[item.key] || item.value
                    }));
            });
            setContent(mergedContent);
            setOriginalContent(JSON.parse(JSON.stringify(mergedContent))); // Deep copy for comparison
            setExpertiseItems(expertiseData);
            setTestimonialItems(testimonialsData);
        } catch (error) {
            console.error('Failed to fetch homepage content:', error);
        } finally{
            setIsLoading(false);
        }
    };
    const handleContentChange = (section, key, value)=>{
        setContent((prev)=>({
                ...prev,
                [section]: prev[section].map((item)=>item.key === key ? {
                        ...item,
                        value
                    } : item)
            }));
    };
    // Expertise management functions
    const addExpertiseItem = ()=>{
        const newItem = {
            title: '',
            description: '',
            icon: '',
            order: expertiseItems.length + 1
        };
        setExpertiseItems((prev)=>[
                ...prev,
                newItem
            ]);
    };
    const updateExpertiseItem = (index, field, value)=>{
        setExpertiseItems((prev)=>prev.map((item, i)=>i === index ? {
                    ...item,
                    [field]: value
                } : item));
    };
    const deleteExpertiseItem = (index)=>{
        setExpertiseItems((prev)=>prev.filter((_, i)=>i !== index));
    };
    // Testimonials management functions
    const addTestimonialItem = ()=>{
        const newItem = {
            text: '',
            author: '',
            company: '',
            avatar: '',
            order: testimonialItems.length + 1
        };
        setTestimonialItems((prev)=>[
                ...prev,
                newItem
            ]);
    };
    const updateTestimonialItem = (index, field, value)=>{
        setTestimonialItems((prev)=>prev.map((item, i)=>i === index ? {
                    ...item,
                    [field]: value
                } : item));
    };
    const deleteTestimonialItem = (index)=>{
        setTestimonialItems((prev)=>prev.filter((_, i)=>i !== index));
    };
    // Handle expertise icon upload
    const handleExpertiseImageUpload = async (index, file)=>{
        const uploadKey = `expertise-${index}-icon`;
        setUploadingImages((prev)=>({
                ...prev,
                [uploadKey]: true
            }));
        try {
            const formData = new FormData();
            formData.append('image', file);
            formData.append('section', 'expertise');
            formData.append('key', `expertise_${index}_icon`);
            const token = localStorage.getItem('adminToken');
            const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:5001/api")}/upload/admin/image`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: formData
            });
            if (response.ok) {
                const result = await response.json();
                updateExpertiseItem(index, 'icon', result.url);
            } else {
                console.error('Image upload failed');
                alert('Image upload failed. Please try again.');
            }
        } catch (error) {
            console.error('Image upload error:', error);
            alert('Image upload failed. Please try again.');
        } finally{
            setUploadingImages((prev)=>({
                    ...prev,
                    [uploadKey]: false
                }));
        }
    };
    // Handle testimonial avatar upload
    const handleTestimonialImageUpload = async (index, file)=>{
        const uploadKey = `testimonial-${index}-avatar`;
        setUploadingImages((prev)=>({
                ...prev,
                [uploadKey]: true
            }));
        try {
            const formData = new FormData();
            formData.append('image', file);
            formData.append('section', 'testimonials');
            formData.append('key', `testimonial_${index}_avatar`);
            const token = localStorage.getItem('adminToken');
            const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:5001/api")}/upload/admin/image`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: formData
            });
            if (response.ok) {
                const result = await response.json();
                updateTestimonialItem(index, 'avatar', result.url);
            } else {
                console.error('Image upload failed');
                alert('Image upload failed. Please try again.');
            }
        } catch (error) {
            console.error('Image upload error:', error);
            alert('Image upload failed. Please try again.');
        } finally{
            setUploadingImages((prev)=>({
                    ...prev,
                    [uploadKey]: false
                }));
        }
    };
    const handleImageUpload = async (section, key, file)=>{
        const uploadKey = `${section}-${key}`;
        setUploadingImages((prev)=>({
                ...prev,
                [uploadKey]: true
            }));
        try {
            const formData = new FormData();
            formData.append('image', file);
            formData.append('section', section);
            formData.append('key', key);
            const token = localStorage.getItem('adminToken');
            const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:5001/api")}/upload/admin/image`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: formData
            });
            if (response.ok) {
                const result = await response.json();
                handleContentChange(section, key, result.url);
            } else {
                console.error('Image upload failed');
                alert('Image upload failed. Please try again.');
            }
        } catch (error) {
            console.error('Image upload error:', error);
            alert('Image upload failed. Please try again.');
        } finally{
            setUploadingImages((prev)=>({
                    ...prev,
                    [uploadKey]: false
                }));
        }
    };
    const saveContent = async ()=>{
        setIsSaving(true);
        try {
            const token = localStorage.getItem('adminToken');
            // Save regular content updates
            const updates = [];
            // Only save items that have changed
            Object.entries(content).forEach(([section, items])=>{
                items.forEach((item)=>{
                    const originalItem = originalContent[section]?.find((orig)=>orig.key === item.key);
                    if (!originalItem || originalItem.value !== item.value) {
                        updates.push({
                            section: item.section,
                            key: item.key,
                            value: item.value,
                            type: item.type,
                            metadata: item.metadata || {},
                            order: item.order || 0
                        });
                    }
                });
            });
            // Save regular content if there are updates
            if (updates.length > 0) {
                const contentResponse = await fetch(`${("TURBOPACK compile-time value", "http://localhost:5001/api")}/admin/homepage/bulk`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        updates
                    })
                });
                if (!contentResponse.ok) {
                    const errorData = await contentResponse.json();
                    console.error('Content save error:', errorData);
                    alert('Failed to save content: ' + (errorData.message || 'Unknown error'));
                    return;
                }
            }
            // Save expertise items
            const expertiseResponse = await fetch(`${("TURBOPACK compile-time value", "http://localhost:5001/api")}/admin/expertise/bulk`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    items: expertiseItems
                })
            });
            if (!expertiseResponse.ok) {
                const errorData = await expertiseResponse.json();
                console.error('Expertise save error:', errorData);
                alert('Failed to save expertise: ' + (errorData.message || 'Unknown error'));
                return;
            }
            // Save testimonials
            const testimonialsResponse = await fetch(`${("TURBOPACK compile-time value", "http://localhost:5001/api")}/admin/testimonials/bulk`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    items: testimonialItems
                })
            });
            if (!testimonialsResponse.ok) {
                const errorData = await testimonialsResponse.json();
                console.error('Testimonials save error:', errorData);
                alert('Failed to save testimonials: ' + (errorData.message || 'Unknown error'));
                return;
            }
            alert('All content saved successfully!');
            await fetchHomepageContent();
        } catch (error) {
            console.error('Save error:', error);
            alert('Failed to save content. Please try again.');
        } finally{
            setIsSaving(false);
        }
    };
    const sections = [
        {
            id: 'header',
            name: 'Header',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Type$3e$__["Type"]
        },
        {
            id: 'hero',
            name: 'Hero Section',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Type$3e$__["Type"]
        },
        {
            id: 'what_we_do',
            name: 'What We Do',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Type$3e$__["Type"]
        },
        {
            id: 'why_choose_us',
            name: 'Why Choose Us',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Type$3e$__["Type"]
        },
        {
            id: 'how_it_works',
            name: 'How It Works',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Type$3e$__["Type"]
        },
        {
            id: 'our_expertise',
            name: 'Our Expertise',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$target$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Target$3e$__["Target"]
        },
        {
            id: 'testimonials',
            name: 'Testimonials',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$square$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageSquare$3e$__["MessageSquare"]
        },
        {
            id: 'final_cta',
            name: 'Final CTA',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Type$3e$__["Type"]
        },
        {
            id: 'footer',
            name: 'Footer & Social',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Type$3e$__["Type"]
        }
    ];
    if (isLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center h-64",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
            }, void 0, false, {
                fileName: "[project]/src/app/admin/homepage/page.tsx",
                lineNumber: 470,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/admin/homepage/page.tsx",
            lineNumber: 469,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-2xl font-bold text-gray-900",
                                children: "Homepage Content"
                            }, void 0, false, {
                                fileName: "[project]/src/app/admin/homepage/page.tsx",
                                lineNumber: 480,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-600",
                                children: "Manage your homepage content (images are now static)"
                            }, void 0, false, {
                                fileName: "[project]/src/app/admin/homepage/page.tsx",
                                lineNumber: 481,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/admin/homepage/page.tsx",
                        lineNumber: 479,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex space-x-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                href: "/",
                                target: "_blank",
                                rel: "noopener noreferrer",
                                className: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__["Eye"], {
                                        className: "h-4 w-4 mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/admin/homepage/page.tsx",
                                        lineNumber: 490,
                                        columnNumber: 13
                                    }, this),
                                    "Preview"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/admin/homepage/page.tsx",
                                lineNumber: 484,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: saveContent,
                                disabled: isSaving,
                                className: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$save$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Save$3e$__["Save"], {
                                        className: "h-4 w-4 mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/admin/homepage/page.tsx",
                                        lineNumber: 498,
                                        columnNumber: 13
                                    }, this),
                                    isSaving ? 'Saving...' : 'Save Changes'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/admin/homepage/page.tsx",
                                lineNumber: 493,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/admin/homepage/page.tsx",
                        lineNumber: 483,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/admin/homepage/page.tsx",
                lineNumber: 478,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 lg:grid-cols-4 gap-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "lg:col-span-1",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                            className: "space-y-1",
                            children: sections.map((section)=>{
                                const Icon = section.icon;
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>setActiveSection(section.id),
                                    className: `w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${activeSection === section.id ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                                            className: "h-4 w-4 mr-3"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                            lineNumber: 520,
                                            columnNumber: 19
                                        }, this),
                                        section.name
                                    ]
                                }, section.id, true, {
                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                    lineNumber: 511,
                                    columnNumber: 17
                                }, this);
                            })
                        }, void 0, false, {
                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                            lineNumber: 507,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/admin/homepage/page.tsx",
                        lineNumber: 506,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "lg:col-span-3",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-white shadow rounded-lg p-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-medium text-gray-900 mb-4",
                                    children: sections.find((s)=>s.id === activeSection)?.name
                                }, void 0, false, {
                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                    lineNumber: 531,
                                    columnNumber: 13
                                }, this),
                                activeSection === 'our_expertise' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-6",
                                    children: [
                                        content.our_expertise?.filter((item)=>item.key === 'title' || item.key === 'description').map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        className: "block text-sm font-medium text-gray-700 capitalize",
                                                        children: item.key.replace(/_/g, ' ')
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                        lineNumber: 541,
                                                        columnNumber: 21
                                                    }, this),
                                                    item.key.includes('description') ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                                        value: item.value,
                                                        onChange: (e)=>handleContentChange(item.section, item.key, e.target.value),
                                                        rows: 3,
                                                        className: "block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white",
                                                        placeholder: `Enter ${item.key.replace(/_/g, ' ')}`
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                        lineNumber: 545,
                                                        columnNumber: 23
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                        type: "text",
                                                        value: item.value,
                                                        onChange: (e)=>handleContentChange(item.section, item.key, e.target.value),
                                                        className: "block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white",
                                                        placeholder: `Enter ${item.key.replace(/_/g, ' ')}`
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                        lineNumber: 553,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, `${item.section}-${item.key}`, true, {
                                                fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                lineNumber: 540,
                                                columnNumber: 19
                                            }, this)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "border-t pt-6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center justify-between mb-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                            className: "text-md font-medium text-gray-900",
                                                            children: "Expertise Items"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                            lineNumber: 567,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: addExpertiseItem,
                                                            className: "inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                    lineNumber: 572,
                                                                    columnNumber: 23
                                                                }, this),
                                                                "Add New"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                            lineNumber: 568,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                    lineNumber: 566,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-4",
                                                    children: expertiseItems.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "border border-gray-200 rounded-lg p-4 space-y-3",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center justify-between",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                                            className: "text-sm font-medium text-gray-700",
                                                                            children: [
                                                                                "Expertise Item ",
                                                                                index + 1
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                            lineNumber: 581,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                            onClick: ()=>deleteExpertiseItem(index),
                                                                            className: "text-red-600 hover:text-red-800",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                lineNumber: 586,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                            lineNumber: 582,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                    lineNumber: 580,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "grid grid-cols-1 gap-3",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                                    className: "block text-sm font-medium text-gray-700",
                                                                                    children: "Title"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                    lineNumber: 592,
                                                                                    columnNumber: 29
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                                    type: "text",
                                                                                    value: item.title,
                                                                                    onChange: (e)=>updateExpertiseItem(index, 'title', e.target.value),
                                                                                    className: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",
                                                                                    placeholder: "Enter expertise title"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                    lineNumber: 593,
                                                                                    columnNumber: 29
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                            lineNumber: 591,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                                    className: "block text-sm font-medium text-gray-700",
                                                                                    children: "Description"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                    lineNumber: 603,
                                                                                    columnNumber: 29
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                                                                    value: item.description,
                                                                                    onChange: (e)=>updateExpertiseItem(index, 'description', e.target.value),
                                                                                    rows: 2,
                                                                                    className: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",
                                                                                    placeholder: "Enter expertise description"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                    lineNumber: 604,
                                                                                    columnNumber: 29
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                            lineNumber: 602,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                                    className: "block text-sm font-medium text-gray-700",
                                                                                    children: "Icon"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                    lineNumber: 614,
                                                                                    columnNumber: 29
                                                                                }, this),
                                                                                item.icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "mt-2 mb-3",
                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                        className: "relative w-16 h-16 border border-gray-300 rounded-md overflow-hidden bg-gray-50",
                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                                            src: item.icon,
                                                                                            alt: "Expertise icon",
                                                                                            fill: true,
                                                                                            className: "object-contain"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                            lineNumber: 620,
                                                                                            columnNumber: 35
                                                                                        }, this)
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                        lineNumber: 619,
                                                                                        columnNumber: 33
                                                                                    }, this)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                    lineNumber: 618,
                                                                                    columnNumber: 31
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "space-y-3",
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                            className: "flex items-center space-x-3",
                                                                                            children: [
                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                                                    className: "cursor-pointer inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500",
                                                                                                    children: [
                                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__["Upload"], {
                                                                                                            className: "h-4 w-4 mr-2"
                                                                                                        }, void 0, false, {
                                                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                                            lineNumber: 634,
                                                                                                            columnNumber: 35
                                                                                                        }, this),
                                                                                                        uploadingImages[`expertise-${index}-icon`] ? 'Uploading...' : 'Upload Icon',
                                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                                                            type: "file",
                                                                                                            accept: "image/*",
                                                                                                            className: "hidden",
                                                                                                            onChange: (e)=>{
                                                                                                                const file = e.target.files?.[0];
                                                                                                                if (file) {
                                                                                                                    handleExpertiseImageUpload(index, file);
                                                                                                                }
                                                                                                            },
                                                                                                            disabled: uploadingImages[`expertise-${index}-icon`]
                                                                                                        }, void 0, false, {
                                                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                                            lineNumber: 636,
                                                                                                            columnNumber: 35
                                                                                                        }, this)
                                                                                                    ]
                                                                                                }, void 0, true, {
                                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                                    lineNumber: 633,
                                                                                                    columnNumber: 33
                                                                                                }, this),
                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                                    className: "text-sm text-gray-500",
                                                                                                    children: "or"
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                                    lineNumber: 649,
                                                                                                    columnNumber: 33
                                                                                                }, this)
                                                                                            ]
                                                                                        }, void 0, true, {
                                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                            lineNumber: 632,
                                                                                            columnNumber: 31
                                                                                        }, this),
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                                            type: "url",
                                                                                            value: item.icon,
                                                                                            onChange: (e)=>updateExpertiseItem(index, 'icon', e.target.value),
                                                                                            className: "block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",
                                                                                            placeholder: "Enter icon URL (64x64 pixels recommended)"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                            lineNumber: 652,
                                                                                            columnNumber: 31
                                                                                        }, this),
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                            className: "text-xs text-gray-500",
                                                                                            children: "Recommended: 64x64 pixels, PNG with transparent background, under 50KB"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                            lineNumber: 660,
                                                                                            columnNumber: 31
                                                                                        }, this)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                    lineNumber: 631,
                                                                                    columnNumber: 29
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                            lineNumber: 613,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                    lineNumber: 590,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, index, true, {
                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                            lineNumber: 579,
                                                            columnNumber: 23
                                                        }, this))
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                    lineNumber: 577,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                            lineNumber: 565,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                    lineNumber: 537,
                                    columnNumber: 15
                                }, this) : activeSection === 'testimonials' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-6",
                                    children: [
                                        content.testimonials?.filter((item)=>item.key === 'title' || item.key === 'description').map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        className: "block text-sm font-medium text-gray-700 capitalize",
                                                        children: item.key.replace(/_/g, ' ')
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                        lineNumber: 676,
                                                        columnNumber: 21
                                                    }, this),
                                                    item.key.includes('description') ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                                        value: item.value,
                                                        onChange: (e)=>handleContentChange(item.section, item.key, e.target.value),
                                                        rows: 3,
                                                        className: "block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white",
                                                        placeholder: `Enter ${item.key.replace(/_/g, ' ')}`
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                        lineNumber: 680,
                                                        columnNumber: 23
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                        type: "text",
                                                        value: item.value,
                                                        onChange: (e)=>handleContentChange(item.section, item.key, e.target.value),
                                                        className: "block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white",
                                                        placeholder: `Enter ${item.key.replace(/_/g, ' ')}`
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                        lineNumber: 688,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, `${item.section}-${item.key}`, true, {
                                                fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                lineNumber: 675,
                                                columnNumber: 19
                                            }, this)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "border-t pt-6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center justify-between mb-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                            className: "text-md font-medium text-gray-900",
                                                            children: "Testimonial Items"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                            lineNumber: 702,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: addTestimonialItem,
                                                            className: "inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                    lineNumber: 707,
                                                                    columnNumber: 23
                                                                }, this),
                                                                "Add New"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                            lineNumber: 703,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                    lineNumber: 701,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-4",
                                                    children: testimonialItems.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "border border-gray-200 rounded-lg p-4 space-y-3",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center justify-between",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                                            className: "text-sm font-medium text-gray-700",
                                                                            children: [
                                                                                "Testimonial ",
                                                                                index + 1
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                            lineNumber: 716,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                            onClick: ()=>deleteTestimonialItem(index),
                                                                            className: "text-red-600 hover:text-red-800",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                lineNumber: 721,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                            lineNumber: 717,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                    lineNumber: 715,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "grid grid-cols-1 gap-3",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                                    className: "block text-sm font-medium text-gray-700",
                                                                                    children: "Testimonial Text"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                    lineNumber: 727,
                                                                                    columnNumber: 29
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                                                                    value: item.text,
                                                                                    onChange: (e)=>updateTestimonialItem(index, 'text', e.target.value),
                                                                                    rows: 3,
                                                                                    className: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",
                                                                                    placeholder: "Enter testimonial text"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                    lineNumber: 728,
                                                                                    columnNumber: 29
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                            lineNumber: 726,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "grid grid-cols-2 gap-3",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                                            className: "block text-sm font-medium text-gray-700",
                                                                                            children: "Author Name"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                            lineNumber: 739,
                                                                                            columnNumber: 31
                                                                                        }, this),
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                                            type: "text",
                                                                                            value: item.author,
                                                                                            onChange: (e)=>updateTestimonialItem(index, 'author', e.target.value),
                                                                                            className: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",
                                                                                            placeholder: "Enter author name"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                            lineNumber: 740,
                                                                                            columnNumber: 31
                                                                                        }, this)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                    lineNumber: 738,
                                                                                    columnNumber: 29
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                                            className: "block text-sm font-medium text-gray-700",
                                                                                            children: "Company"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                            lineNumber: 750,
                                                                                            columnNumber: 31
                                                                                        }, this),
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                                            type: "text",
                                                                                            value: item.company,
                                                                                            onChange: (e)=>updateTestimonialItem(index, 'company', e.target.value),
                                                                                            className: "mt-1 block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",
                                                                                            placeholder: "Enter company name"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                            lineNumber: 751,
                                                                                            columnNumber: 31
                                                                                        }, this)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                    lineNumber: 749,
                                                                                    columnNumber: 29
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                            lineNumber: 737,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                                    className: "block text-sm font-medium text-gray-700",
                                                                                    children: "Author Avatar (Optional)"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                    lineNumber: 762,
                                                                                    columnNumber: 29
                                                                                }, this),
                                                                                item.avatar && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "mt-2 mb-3",
                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                        className: "relative w-16 h-16 border border-gray-300 rounded-full overflow-hidden bg-gray-50",
                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                                            src: item.avatar,
                                                                                            alt: "Author avatar",
                                                                                            fill: true,
                                                                                            className: "object-cover"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                            lineNumber: 768,
                                                                                            columnNumber: 35
                                                                                        }, this)
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                        lineNumber: 767,
                                                                                        columnNumber: 33
                                                                                    }, this)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                    lineNumber: 766,
                                                                                    columnNumber: 31
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "space-y-3",
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                            className: "flex items-center space-x-3",
                                                                                            children: [
                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                                                    className: "cursor-pointer inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500",
                                                                                                    children: [
                                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__["Upload"], {
                                                                                                            className: "h-4 w-4 mr-2"
                                                                                                        }, void 0, false, {
                                                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                                            lineNumber: 782,
                                                                                                            columnNumber: 35
                                                                                                        }, this),
                                                                                                        uploadingImages[`testimonial-${index}-avatar`] ? 'Uploading...' : 'Upload Avatar',
                                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                                                            type: "file",
                                                                                                            accept: "image/*",
                                                                                                            className: "hidden",
                                                                                                            onChange: (e)=>{
                                                                                                                const file = e.target.files?.[0];
                                                                                                                if (file) {
                                                                                                                    handleTestimonialImageUpload(index, file);
                                                                                                                }
                                                                                                            },
                                                                                                            disabled: uploadingImages[`testimonial-${index}-avatar`]
                                                                                                        }, void 0, false, {
                                                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                                            lineNumber: 784,
                                                                                                            columnNumber: 35
                                                                                                        }, this)
                                                                                                    ]
                                                                                                }, void 0, true, {
                                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                                    lineNumber: 781,
                                                                                                    columnNumber: 33
                                                                                                }, this),
                                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                                    className: "text-sm text-gray-500",
                                                                                                    children: "or"
                                                                                                }, void 0, false, {
                                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                                    lineNumber: 797,
                                                                                                    columnNumber: 33
                                                                                                }, this)
                                                                                            ]
                                                                                        }, void 0, true, {
                                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                            lineNumber: 780,
                                                                                            columnNumber: 31
                                                                                        }, this),
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                                            type: "url",
                                                                                            value: item.avatar || '',
                                                                                            onChange: (e)=>updateTestimonialItem(index, 'avatar', e.target.value),
                                                                                            className: "block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white",
                                                                                            placeholder: "Enter avatar URL (optional)"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                            lineNumber: 800,
                                                                                            columnNumber: 31
                                                                                        }, this),
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                            className: "text-xs text-gray-500",
                                                                                            children: "Recommended: 100x100 pixels, square image, under 100KB"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                            lineNumber: 808,
                                                                                            columnNumber: 31
                                                                                        }, this)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                                    lineNumber: 779,
                                                                                    columnNumber: 29
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                            lineNumber: 761,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                    lineNumber: 725,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, index, true, {
                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                            lineNumber: 714,
                                                            columnNumber: 23
                                                        }, this))
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                    lineNumber: 712,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                            lineNumber: 700,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                    lineNumber: 672,
                                    columnNumber: 15
                                }, this) : /* Regular content sections */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-6",
                                    children: content[activeSection]?.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                    className: "block text-sm font-medium text-gray-700 capitalize",
                                                    children: item.key.replace(/_/g, ' ')
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                    lineNumber: 824,
                                                    columnNumber: 21
                                                }, this),
                                                item.type === 'image' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-3",
                                                    children: [
                                                        item.value && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "relative w-full h-48 border border-gray-300 rounded-md overflow-hidden",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                src: item.value,
                                                                alt: item.key.replace(/_/g, ' '),
                                                                fill: true,
                                                                className: "object-cover"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                lineNumber: 833,
                                                                columnNumber: 29
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                            lineNumber: 832,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center space-x-3",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                    className: "cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__["Upload"], {
                                                                            className: "h-4 w-4 mr-2"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                            lineNumber: 845,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        uploadingImages[`${item.section}-${item.key}`] ? 'Uploading...' : 'Upload Image',
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                            type: "file",
                                                                            accept: "image/*",
                                                                            className: "hidden",
                                                                            onChange: (e)=>{
                                                                                const file = e.target.files?.[0];
                                                                                if (file) {
                                                                                    handleImageUpload(item.section, item.key, file);
                                                                                }
                                                                            },
                                                                            disabled: uploadingImages[`${item.section}-${item.key}`]
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                            lineNumber: 847,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                    lineNumber: 844,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                    type: "url",
                                                                    value: item.value,
                                                                    onChange: (e)=>handleContentChange(item.section, item.key, e.target.value),
                                                                    className: "flex-1 border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white",
                                                                    placeholder: "Or enter image URL"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                                    lineNumber: 862,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                            lineNumber: 843,
                                                            columnNumber: 25
                                                        }, this),
                                                        item.key === 'background_image' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-xs text-gray-500",
                                                            children: "Recommended: 1920x1080 pixels (16:9 aspect ratio), under 500KB"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                            lineNumber: 873,
                                                            columnNumber: 27
                                                        }, this),
                                                        item.key === 'logo' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-xs text-gray-500",
                                                            children: "Recommended: 300x100 pixels (3:1 aspect ratio), PNG with transparent background, under 100KB"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                            lineNumber: 878,
                                                            columnNumber: 27
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                    lineNumber: 829,
                                                    columnNumber: 23
                                                }, this) : item.key.includes('description') || item.key.includes('subtitle') ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                                    value: item.value,
                                                    onChange: (e)=>handleContentChange(item.section, item.key, e.target.value),
                                                    rows: 3,
                                                    className: "block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white",
                                                    placeholder: `Enter ${item.key.replace(/_/g, ' ')}`
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                    lineNumber: 884,
                                                    columnNumber: 23
                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                    type: "text",
                                                    value: item.value,
                                                    onChange: (e)=>handleContentChange(item.section, item.key, e.target.value),
                                                    className: "block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder-gray-500 bg-white",
                                                    placeholder: `Enter ${item.key.replace(/_/g, ' ')}`
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                                    lineNumber: 892,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, `${item.section}-${item.key}`, true, {
                                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                                            lineNumber: 823,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/app/admin/homepage/page.tsx",
                                    lineNumber: 821,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/admin/homepage/page.tsx",
                            lineNumber: 530,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/admin/homepage/page.tsx",
                        lineNumber: 529,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/admin/homepage/page.tsx",
                lineNumber: 504,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/admin/homepage/page.tsx",
        lineNumber: 476,
        columnNumber: 5
    }, this);
}
}),

};

//# sourceMappingURL=src_app_admin_homepage_page_tsx_8e786efd._.js.map