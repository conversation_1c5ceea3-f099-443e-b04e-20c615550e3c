{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/src/app/admin/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport {\n  Home,\n  Settings,\n  FileText,\n  Image,\n  Users,\n  BarChart3,\n  LogOut,\n  Menu,\n  X\n} from 'lucide-react';\n\ninterface AdminUser {\n  id: string;\n  email: string;\n  name: string;\n  role: string;\n}\n\nexport default function AdminLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const [user, setUser] = useState<AdminUser | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      const token = localStorage.getItem('adminToken');\n      \n      if (!token) {\n        router.push('/admin/login');\n        return;\n      }\n\n      try {\n        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/verify`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n          },\n        });\n\n        if (response.ok) {\n          const data = await response.json();\n          if (data.user.role === 'admin') {\n            setUser(data.user);\n          } else {\n            localStorage.removeItem('adminToken');\n            localStorage.removeItem('adminUser');\n            router.push('/admin/login');\n          }\n        } else {\n          localStorage.removeItem('adminToken');\n          localStorage.removeItem('adminUser');\n          router.push('/admin/login');\n        }\n      } catch (error) {\n        console.error('Auth check failed:', error);\n        router.push('/admin/login');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    if (pathname !== '/admin/login') {\n      checkAuth();\n    } else {\n      setIsLoading(false);\n    }\n  }, [router, pathname]);\n\n  const handleLogout = () => {\n    localStorage.removeItem('adminToken');\n    localStorage.removeItem('adminUser');\n    router.push('/admin/login');\n  };\n\n  const navigation = [\n    { name: 'Dashboard', href: '/admin/dashboard', icon: Home },\n    { name: 'Homepage Content', href: '/admin/homepage', icon: FileText },\n    { name: 'Media Library', href: '/admin/media', icon: Image },\n    { name: 'Leads', href: '/admin/leads', icon: Users },\n    { name: 'Analytics', href: '/admin/analytics', icon: BarChart3 },\n    { name: 'Settings', href: '/admin/settings', icon: Settings },\n  ];\n\n  if (pathname === '/admin/login') {\n    return children;\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex\">\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div className=\"fixed inset-0 z-40 lg:hidden\">\n          <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        </div>\n      )}\n\n      {/* Sidebar */}\n      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 lg:flex lg:flex-col`}>\n        <div className=\"flex items-center justify-between h-16 px-6 border-b border-gray-200\">\n          <h1 className=\"text-xl font-bold text-gray-900\">RevAdOps Admin</h1>\n          <button\n            onClick={() => setSidebarOpen(false)}\n            className=\"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        <nav className=\"mt-6 px-3\">\n          <div className=\"space-y-1\">\n            {navigation.map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n              \n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${\n                    isActive\n                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n                  }`}\n                  onClick={() => setSidebarOpen(false)}\n                >\n                  <Icon className={`mr-3 h-5 w-5 ${isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'}`} />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </div>\n        </nav>\n\n        {/* User info and logout */}\n        <div className=\"absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200\">\n          <div className=\"flex items-center mb-3\">\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-medium text-gray-900 truncate\">{user.name}</p>\n              <p className=\"text-xs text-gray-500 truncate\">{user.email}</p>\n            </div>\n          </div>\n          <button\n            onClick={handleLogout}\n            className=\"w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors duration-200\"\n          >\n            <LogOut className=\"mr-3 h-5 w-5 text-gray-400\" />\n            Sign out\n          </button>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64 flex-1 flex flex-col min-h-screen\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"flex items-center justify-between h-16 px-6\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className=\"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600\"\n            >\n              <Menu className=\"h-6 w-6\" />\n            </button>\n            \n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-500\">Welcome back, {user.name}</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 p-6 overflow-auto\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAwBe,SAAS,YAAY,EAClC,QAAQ,EAGT;IACC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,IAAI,CAAC,OAAO;gBACV,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,iEAAmC,YAAY,CAAC,EAAE;oBAC7E,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBACpC;gBACF;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,SAAS;wBAC9B,QAAQ,KAAK,IAAI;oBACnB,OAAO;wBACL,aAAa,UAAU,CAAC;wBACxB,aAAa,UAAU,CAAC;wBACxB,OAAO,IAAI,CAAC;oBACd;gBACF,OAAO;oBACL,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,OAAO,IAAI,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,OAAO,IAAI,CAAC;YACd,SAAU;gBACR,aAAa;YACf;QACF;QAEA,IAAI,aAAa,gBAAgB;YAC/B;QACF,OAAO;YACL,aAAa;QACf;IACF,GAAG;QAAC;QAAQ;KAAS;IAErB,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAoB,MAAM,mMAAA,CAAA,OAAI;QAAC;QAC1D;YAAE,MAAM;YAAoB,MAAM;YAAmB,MAAM,8MAAA,CAAA,WAAQ;QAAC;QACpE;YAAE,MAAM;YAAiB,MAAM;YAAgB,MAAM,oMAAA,CAAA,QAAK;QAAC;QAC3D;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM,oMAAA,CAAA,QAAK;QAAC;QACnD;YAAE,MAAM;YAAa,MAAM;YAAoB,MAAM,kNAAA,CAAA,YAAS;QAAC;QAC/D;YAAE,MAAM;YAAY,MAAM;YAAmB,MAAM,0MAAA,CAAA,WAAQ;QAAC;KAC7D;IAED,IAAI,aAAa,gBAAgB;QAC/B,OAAO;IACT;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAA0C,SAAS,IAAM,eAAe;;;;;;;;;;;0BAK3F,8OAAC;gBAAI,WAAW,CAAC,8DAA8D,EAAE,cAAc,kBAAkB,oBAAoB,wGAAwG,CAAC;;kCAC5O,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,8OAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,gGAAgG,EAC1G,WACI,wDACA,sDACJ;oCACF,SAAS,IAAM,eAAe;;sDAE9B,8OAAC;4CAAK,WAAW,CAAC,aAAa,EAAE,WAAW,kBAAkB,2CAA2C;;;;;;wCACxG,KAAK,IAAI;;mCAVL,KAAK,IAAI;;;;;4BAapB;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAA8C,KAAK,IAAI;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAAkC,KAAK,KAAK;;;;;;;;;;;;;;;;;0CAG7D,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;;;;;;;;;;;;;0BAOvD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAGlB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;;4CAAwB;4CAAe,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;kCAMtE,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}