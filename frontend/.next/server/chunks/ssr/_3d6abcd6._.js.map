{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunase<PERSON>an/frontend/src/app/admin/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport {\n  FileText,\n  Image,\n  Users,\n  Eye\n} from 'lucide-react';\n\ninterface DashboardStats {\n  totalLeads: number;\n  totalContent: number;\n  totalMedia: number;\n  recentActivity: Array<{\n    id: string;\n    type: string;\n    description: string;\n    timestamp: string;\n  }>;\n}\n\nexport default function AdminDashboard() {\n  const [stats, setStats] = useState<DashboardStats>({\n    totalLeads: 0,\n    totalContent: 0,\n    totalMedia: 0,\n    recentActivity: []\n  });\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      // Simulate API calls for now\n      // In real implementation, you would fetch actual data\n      setStats({\n        totalLeads: 45,\n        totalContent: 28,\n        totalMedia: 156,\n        recentActivity: [\n          {\n            id: '1',\n            type: 'content',\n            description: 'Homepage hero section updated',\n            timestamp: new Date().toISOString()\n          },\n          {\n            id: '2',\n            type: 'lead',\n            description: 'New lead from contact form',\n            timestamp: new Date(Date.now() - 3600000).toISOString()\n          },\n          {\n            id: '3',\n            type: 'media',\n            description: 'New image uploaded to media library',\n            timestamp: new Date(Date.now() - 7200000).toISOString()\n          }\n        ]\n      });\n    } catch (error) {\n      console.error('Failed to fetch dashboard data:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const statCards = [\n    {\n      name: 'Total Leads',\n      value: stats.totalLeads,\n      icon: Users,\n      color: 'bg-blue-500',\n      href: '/admin/leads'\n    },\n    {\n      name: 'Content Pieces',\n      value: stats.totalContent,\n      icon: FileText,\n      color: 'bg-green-500',\n      href: '/admin/homepage'\n    },\n    {\n      name: 'Media Assets',\n      value: stats.totalMedia,\n      icon: Image,\n      color: 'bg-purple-500',\n      href: '/admin/media'\n    },\n    {\n      name: 'Page Views',\n      value: '12.5K',\n      icon: Eye,\n      color: 'bg-orange-500',\n      href: '/admin/analytics'\n    }\n  ];\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-2xl font-bold text-gray-900\">Dashboard</h1>\n        <p className=\"text-gray-600\">Welcome to your RevAdOps admin panel</p>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {statCards.map((card) => {\n          const Icon = card.icon;\n          return (\n            <div\n              key={card.name}\n              className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">{card.name}</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{card.value}</p>\n                </div>\n                <div className={`p-3 rounded-full ${card.color}`}>\n                  <Icon className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Quick Actions Card */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Quick Actions</h2>\n          <div className=\"space-y-3\">\n            <a\n              href=\"/admin/homepage\"\n              className=\"flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200\"\n            >\n              <FileText className=\"h-5 w-5 text-blue-500 mr-3\" />\n              <div>\n                <p className=\"font-medium text-gray-900\">Edit Homepage</p>\n                <p className=\"text-sm text-gray-600\">Update homepage content and images</p>\n              </div>\n            </a>\n            \n            <a\n              href=\"/admin/media\"\n              className=\"flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200\"\n            >\n              <Image className=\"h-5 w-5 text-green-500 mr-3\" aria-label=\"Upload Media\" />\n              <div>\n                <p className=\"font-medium text-gray-900\">Upload Media</p>\n                <p className=\"text-sm text-gray-600\">Add new images and videos</p>\n              </div>\n            </a>\n            \n            <a\n              href=\"/admin/leads\"\n              className=\"flex items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200\"\n            >\n              <Users className=\"h-5 w-5 text-purple-500 mr-3\" />\n              <div>\n                <p className=\"font-medium text-gray-900\">View Leads</p>\n                <p className=\"text-sm text-gray-600\">Manage customer inquiries</p>\n              </div>\n            </a>\n          </div>\n        </div>\n\n        {/* Recent Activity */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Recent Activity</h2>\n          <div className=\"space-y-4\">\n            {stats.recentActivity.map((activity) => (\n              <div key={activity.id} className=\"flex items-start space-x-3\">\n                <div className=\"flex-shrink-0\">\n                  {activity.type === 'content' && (\n                    <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                      <FileText className=\"h-4 w-4 text-blue-600\" />\n                    </div>\n                  )}\n                  {activity.type === 'lead' && (\n                    <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\">\n                      <Users className=\"h-4 w-4 text-green-600\" />\n                    </div>\n                  )}\n                  {activity.type === 'media' && (\n                    <div className=\"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\">\n                      <Image className=\"h-4 w-4 text-purple-600\" aria-label=\"Media Activity\" />\n                    </div>\n                  )}\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm text-gray-900\">{activity.description}</p>\n                  <p className=\"text-xs text-gray-500\">\n                    {new Date(activity.timestamp).toLocaleString()}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Website Preview */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Website Preview</h2>\n          <a\n            href=\"/\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50\"\n          >\n            <Eye className=\"h-4 w-4 mr-2\" />\n            View Live Site\n          </a>\n        </div>\n        <div className=\"bg-gray-100 rounded-lg p-4\">\n          <p className=\"text-gray-600 text-center\">\n            Preview your website changes here. Click &quot;View Live Site&quot; to see the current website.\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAsBe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,YAAY;QACZ,cAAc;QACd,YAAY;QACZ,gBAAgB,EAAE;IACpB;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI;YACF,6BAA6B;YAC7B,sDAAsD;YACtD,SAAS;gBACP,YAAY;gBACZ,cAAc;gBACd,YAAY;gBACZ,gBAAgB;oBACd;wBACE,IAAI;wBACJ,MAAM;wBACN,aAAa;wBACb,WAAW,IAAI,OAAO,WAAW;oBACnC;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,aAAa;wBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,WAAW;oBACvD;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,aAAa;wBACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,WAAW;oBACvD;iBACD;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,YAAY;QAChB;YACE,MAAM;YACN,OAAO,MAAM,UAAU;YACvB,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,MAAM,YAAY;YACzB,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,MAAM,UAAU;YACvB,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,MAAM;QACR;KACD;IAED,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC;oBACd,MAAM,OAAO,KAAK,IAAI;oBACtB,qBACE,8OAAC;wBAEC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAqC,KAAK,IAAI;;;;;;sDAC3D,8OAAC;4CAAE,WAAU;sDAAoC,KAAK,KAAK;;;;;;;;;;;;8CAE7D,8OAAC;oCAAI,WAAW,CAAC,iBAAiB,EAAE,KAAK,KAAK,EAAE;8CAC9C,cAAA,8OAAC;wCAAK,WAAU;;;;;;;;;;;;;;;;;uBATf,KAAK,IAAI;;;;;gBAcpB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAA4B;;;;;;kEACzC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAIzC,8OAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;gDAA8B,cAAW;;;;;;0DAC1D,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAA4B;;;;;;kEACzC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAIzC,8OAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAA4B;;;;;;kEACzC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAI,WAAU;0CACZ,MAAM,cAAc,CAAC,GAAG,CAAC,CAAC,yBACzB,8OAAC;wCAAsB,WAAU;;0DAC/B,8OAAC;gDAAI,WAAU;;oDACZ,SAAS,IAAI,KAAK,2BACjB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;oDAGvB,SAAS,IAAI,KAAK,wBACjB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;oDAGpB,SAAS,IAAI,KAAK,yBACjB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;4DAA0B,cAAW;;;;;;;;;;;;;;;;;0DAI5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAyB,SAAS,WAAW;;;;;;kEAC1D,8OAAC;wDAAE,WAAU;kEACV,IAAI,KAAK,SAAS,SAAS,EAAE,cAAc;;;;;;;;;;;;;uCArBxC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;0BA+B7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;;kDAEV,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAIpC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAA4B;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/eye.js", "sources": ["file:///Users/<USER>/Documents/augment-projects/Gunaselaan/frontend/node_modules/lucide-react/src/icons/eye.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0',\n      key: '1nclc0',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Eye\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi4wNjIgMTIuMzQ4YTEgMSAwIDAgMSAwLS42OTYgMTAuNzUgMTAuNzUgMCAwIDEgMTkuODc2IDAgMSAxIDAgMCAxIDAgLjY5NiAxMC43NSAxMC43NSAwIDAgMS0xOS44NzYgMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Eye = createLucideIcon('eye', __iconNode);\n\nexport default Eye;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU;YAAE,CAAA,CAAA,CAAA,CAAI,IAAA,CAAA;YAAM,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA;YAAK,CAAA,CAAA,CAAA,CAAA,CAAK,QAAA;QAAA,CAAU;KAAA;CAC1D;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,wKAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}]}