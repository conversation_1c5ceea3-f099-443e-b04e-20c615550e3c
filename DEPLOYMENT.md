# Deployment Guide

## Issue Resolution: Prisma Client Generation

### Problem
When deploying to Render and Vercel, you encountered the error:
```
TypeError: Cannot read properties of undefined (reading 'findMany')
```

This error occurs because the Prisma client wasn't properly generated during the deployment process.

### Root Cause
The deployment platforms need to:
1. Install dependencies
2. Generate the Prisma client
3. Run database migrations
4. Start the application

### Solution Implemented

#### 1. Updated package.json Scripts
Added the following scripts to `backend/package.json`:

```json
{
  "scripts": {
    "build": "prisma generate",
    "postinstall": "prisma generate",
    "deploy": "prisma migrate deploy && prisma generate",
    "deploy:setup": "node scripts/deploy-setup.js",
    "db:migrate": "prisma migrate deploy",
    "db:generate": "prisma generate",
    "db:reset": "prisma migrate reset --force"
  }
}
```

#### 2. Enhanced Database Configuration
Updated `backend/src/config/database.js` to:
- Test database connection on startup
- Verify Prisma client has required models
- Exit gracefully if models are missing

#### 3. Created Deployment Setup Script
Added `backend/scripts/deploy-setup.js` to:
- Test database connectivity
- Verify table existence
- Check model accessibility

## Deployment Instructions

### For Render

1. **Build Command**: `npm run build`
2. **Start Command**: `npm start`
3. **Environment Variables**: Ensure `DATABASE_URL` is set

### For Vercel

1. **Build Command**: `cd backend && npm run build`
2. **Install Command**: `cd backend && npm install`
3. **Start Command**: `cd backend && npm start`
4. **Environment Variables**: Set `DATABASE_URL` in Vercel dashboard

### For Railway/Heroku

1. **Build Command**: `npm run deploy`
2. **Start Command**: `npm start`
3. **Environment Variables**: Set `DATABASE_URL`

## Environment Variables Required

```env
DATABASE_URL="your_postgresql_connection_string"
NODE_ENV="production"
JWT_SECRET="your_jwt_secret"
CLOUDINARY_CLOUD_NAME="your_cloudinary_name"
CLOUDINARY_API_KEY="your_cloudinary_key"
CLOUDINARY_API_SECRET="your_cloudinary_secret"
```

## Troubleshooting

### If you still get Prisma errors:

1. **Manual Generation**: Run `npx prisma generate` in your deployment
2. **Check Logs**: Look for Prisma generation errors in build logs
3. **Database Migration**: Ensure `npx prisma migrate deploy` runs successfully
4. **Test Setup**: Run `npm run deploy:setup` to verify everything works

### Common Issues:

1. **Missing DATABASE_URL**: Ensure it's set in your deployment platform
2. **Migration Failures**: Check if your database is accessible
3. **Build Timeouts**: Increase build timeout if Prisma generation takes too long
4. **Route Conflicts**: Fixed path-to-regexp error by reordering routes in lead.js

### Additional Fixes Applied:

**Route Ordering Issues**: Fixed multiple `path-to-regexp` errors caused by route conflicts:

1. **Lead Routes**: In `backend/src/routes/lead.js`, moved `/admin/stats` before `/admin/:id`
2. **Admin Routes**: In `backend/src/routes/admin.js`, moved `/homepage/all` before `/content/:page`

The issue was that specific routes were defined after parameterized routes, causing Express to try to parse specific strings as parameters.

**Build Script Enhancement**: Updated the build script to use `prisma generate --no-engine` as recommended for production deployments, which reduces bundle size and improves performance.

## Verification

After deployment, your application should:
1. Connect to the database successfully
2. Have all Prisma models available
3. Handle API requests without "undefined" errors

The enhanced error checking will now catch these issues early and provide clear error messages.
